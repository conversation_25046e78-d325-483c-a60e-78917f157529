<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Find Gigs</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
    <style>
    :root {
        --primary-blue: #004AAD;
        --primary-pink: #CD208B;
        --yellow: #FFD700;
        --text-dark: #000000;
        --text-light: #FFFFFF;
        --text-gray: #666;
    }
    html {
        font-size: 16px;
    }
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        transition: all 0.3s ease-in-out;
        overflow-wrap: break-word;
        word-wrap: break-word;
        word-break: break-word;
        hyphens: auto;
    }
    body {
        font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        padding-top: 0;
        max-width: 100%;
        overflow-x: hidden;
    }
    .navbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 1%;
        position: fixed;
        width: 100%;
        top: 0;
        z-index: 1000;
        background: white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        height: 80px;
    }
    .logo {
        display: flex;
        align-items: center;
        font-size: 1.4rem;
        font-weight: 600;
        color: var(--primary-pink);
    }
    .logo img {
        width: 60px;
        height: 60px;
    }
    .logo:hover {
        color: var(--primary-blue);
    }
    .nav-links {
        display: flex;
        gap: 1.5rem;
        align-items: center;
        margin-left: 1.5rem;
    }
    .nav-links a {
        font-weight: 500;
        font-size: 1rem;
        color: var(--primary-blue);
        text-decoration: none;
        transition: color 0.3s ease;
        padding: 0.5rem 0.9rem;
        white-space: nowrap;
    }
    .nav-links a:hover {
        color: var(--primary-pink);
    }
    .right-section {
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    .search-container {
        display: flex;
        height: 40px;
        margin-right: 1rem;
    }
    .search-type-select {
        position: relative;
        height: 100%;
    }
    .search-type-button {
        height: 40px;
        background: white;
        border: 2px solid var(--primary-blue);
        border-right: none;
        border-radius: 8px 0 0 8px;
        padding: 0 1rem;
        color: var(--primary-blue);
        font-size: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
    }
    .search-type-button:hover {
        color: var(--primary-pink);
        border-color: var(--primary-pink);
    }
    .search-type-button:after {
        content: '▼';
        font-size: 0.8rem;
    }
    .search-type-dropdown {
        position: absolute;
        top: 45px;
        left: 0;
        background: white;
        border: 2px solid var(--primary-blue);
        border-radius: 8px;
        margin-top: 0.5rem;
        min-width: 120px;
        display: none;
        z-index: 1000;
    }
    .search-type-dropdown.active {
        display: block;
    }
    .search-type-option {
        padding: 1rem 1.5rem;
        cursor: pointer;
        color: var(--primary-blue);
    }
    .search-type-option:hover {
        background: #f5f5f5;
        color: var(--primary-pink);
    }
    .search-bar {
        height: 40px;
        display: flex;
        align-items: center;
        background: white;
        border: 2px solid var(--primary-blue);
        border-radius: 0 8px 8px 0;
        width: 200px;
    }
    .search-bar:hover {
        border-color: var(--primary-pink);
    }
    .search-bar input {
        border: none;
        outline: none;
        padding: 0 1rem;
        width: 100%;
        height: 100%;
        font-size: 1rem;
    }
    .search-bar .icon {
        color: var(--primary-blue);
        padding: 0 0.5rem;
        font-size: 1rem;
    }
    .search-bar:hover .icon {
        color: var(--primary-pink);
    }
    .auth-buttons {
        display: flex;
        gap: 1rem;
        align-items: center;
        position: relative;
    }
    .btn {
        padding: 0 1.5rem;
        border-radius: 8px;
        font-size: 0.9rem;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 100px;
        height: 40px;
    }
    .navbar .btn {
        min-width: 100px;
        height: 40px;
        padding: 0 1.5rem;
        font-size: 0.9rem;
    }
    .modal .btn {
        width: 100%;
        height: 40px;
        margin: 0.5rem 0;
    }
    .btn-primary {
        background: var(--primary-pink);
        border: 2px solid var(--primary-pink);
        color: var(--text-light);
    }
    .btn-outline {
        background: white;
        border: 2px solid var(--primary-blue);
        color: var(--primary-blue);
    }
    .btn-gigs {
        background: var(--primary-blue);
        border: 2px solid var(--primary-blue);
        color: var(--text-light);
    }
    .btn-primary:hover {
        background: white;
        border: 2px solid var(--primary-pink);
        color: var(--primary-pink);
        text-decoration: none;
    }
    .btn-outline:hover {
        background: var(--primary-blue);
        color: var(--text-light);
        text-decoration: none;
    }
    .btn-gigs:hover {
        background: white;
        color: var(--primary-blue);
    }
    .navbar .auth-buttons {
        gap: 0.8rem;
    }
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.4);
        justify-content: center;
        align-items: center;
    }
    .modal-content {
        background-color: var(--text-light);
        padding: 2rem;
        border-radius: 10px;
        width: 90%;
        max-width: 500px;
        position: relative;
        text-align: center;
    }
    .modal-content h2 {
        font-size: 24px;
        color: var(--primary-blue);
    }
    .modal-buttons {
        margin-top: 1.5rem;
        display: flex;
        justify-content: center;
        padding: 0 2rem;
    }
    .modal-buttons .btn {
        width: auto;
        min-width: 160px;
        max-width: 80%;
        margin: 0 auto;
    }
    .close {
        position: absolute;
        right: 1rem;
        top: 0.1rem;
        font-size: 2rem;
        cursor: pointer;
    }
    .role-selection {
        margin: 2rem 0;
    }
    .role-option {
        border: 2px solid var(--primary-blue);
        border-radius: 10px;
        padding: 1rem;
        margin: 1rem 0;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    .role-option:hover {
        background-color: var(--text-light);
    }
    .role-option.selected {
        border-color: var(--primary-pink);
        background-color: var(--text-light);
    }
    .role-option input[type="radio"] {
        margin-right: 0.5rem;
    }
    .login-link {
        text-align: center;
        margin-top: 15px;
        font-size: 0.9rem;
        color: var(--text-gray);
    }
    .login-link a {
        color: var(--primary-blue);
        text-decoration: none;
        font-weight: 500;
    }
    .login-link a:hover {
        text-decoration: underline;
    }
    /* Hamburger Menu Styles */
    .hamburger {
        position: relative;
        display: none;
        cursor: pointer;
        padding: 15px;
        background: none;
        border: none;
        z-index: 1001;
    }
    .hamburger span {
        display: block;
        width: 25px;
        height: 3px;
        margin: 5px 0;
        background-color: var(--primary-blue);
        transition: 0.3s;
    }
    /* Side nav styles */
    .side-nav {
        position: fixed;
        top: 2rem;
        left: -100%;
        height: 100vh;
        width: 100%;
        background-color: white;
        z-index: 1000;
        transition: left 0.3s ease;
    }
    .side-nav.active {
        left: 0;
    }
    .side-nav-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100vh;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 999;
        display: none;
    }
    .side-nav-overlay.active {
        display: block;
    }
    .side-nav-content {
        padding: 60px 0 20px 0;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
    }
    .nav-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
        width: 100%;
        max-width: 300px;
        padding: 20px;
        color: var(--primary-blue);
        text-decoration: none;
        border-bottom: 1px solid #eee;
        transition: background-color 0.2s ease;
        font-size: 1.2rem;
        text-align: center;
        margin: 10px 0;
        font-weight: 500;
    }
    .nav-item:hover {
        color: var(--primary-pink);
    }
    .nav-item.active {
        color: var(--primary-pink);
        font-weight: 700;
    }
    .side-nav.active + .hamburger span {
        background-color: var(--primary-blue);
    }
    /* Prevent body scroll when menu is open */
    body.menu-open {
        overflow: hidden;
        position: fixed;
        width: 100%;
    }
    /* Mobile Search Icon */
    .mobile-search-icon {
        display: none;
        background: none;
        border: none;
        color: var(--primary-blue);
        font-size: 1.5rem;
        cursor: pointer;
        padding: 0.5rem;
        transition: color 0.3s ease;
    }
    .mobile-search-icon:hover {
        color: var(--primary-pink);
    }
    /* Expandable Search Overlay */
    .search-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100vh;
        background: rgba(0, 0, 0, 0.8);
        z-index: 2000;
        display: none;
        align-items: flex-start;
        justify-content: center;
        padding-top: 120px;
    }
    .search-overlay.active {
        display: flex;
    }
    .expanded-search-container {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        width: 90%;
        max-width: 500px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        transform: scale(0.8);
        opacity: 0;
        transition: all 0.3s ease;
    }
    .search-overlay.active .expanded-search-container {
        transform: scale(1);
        opacity: 1;
    }
    .expanded-search-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }
    .expanded-search-header h3 {
        color: var(--primary-blue);
        font-size: 1.5rem;
        margin: 0;
    }
    .close-search-btn {
        background: none;
        border: none;
        font-size: 2rem;
        color: var(--primary-blue);
        cursor: pointer;
        padding: 0;
        line-height: 1;
    }
    .close-search-btn:hover {
        color: var(--primary-pink);
    }
    .expanded-search-form {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    .expanded-search-type {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }
    .search-type-chip {
        padding: 0.5rem 1rem;
        border: 2px solid var(--primary-blue);
        border-radius: 20px;
        background: white;
        color: var(--primary-blue);
        cursor: pointer;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }
    .search-type-chip.active,
    .search-type-chip:hover {
        background: var(--primary-blue);
        color: white;
    }
    .expanded-search-input {
        display: flex;
        align-items: center;
        border: 2px solid var(--primary-blue);
        border-radius: 8px;
        background: white;
        height: 50px;
    }
    .expanded-search-input input {
        flex: 1;
        border: none;
        outline: none;
        padding: 0 1rem;
        font-size: 1.1rem;
        height: 100%;
    }
    .expanded-search-input .search-btn {
        background: var(--primary-blue);
        color: white;
        border: none;
        padding: 0 1.5rem;
        height: 100%;
        border-radius: 0 6px 6px 0;
        cursor: pointer;
        font-size: 1rem;
        transition: background 0.3s ease;
    }
    .expanded-search-input .search-btn:hover {
        background: var(--primary-pink);
    }
    /* Mobile find buttons in hamburger menu */
    .mobile-find-buttons {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-top: 2rem;
        width: 100%;
        max-width: 250px;
    }
    .mobile-find-buttons .btn {
        width: 100%;
        padding: 1rem 2rem;
        font-size: 1.2rem;
        border-radius: 8px;
        text-align: center;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    .mobile-find-buttons .btn-primary {
        background: var(--primary-pink);
        color: white;
        border: 2px solid var(--primary-pink);
    }
    .mobile-find-buttons .btn-primary:hover {
        background: transparent;
        color: var(--primary-pink);
    }
    .mobile-find-buttons .btn-gigs {
        background: var(--primary-blue);
        color: white;
        border: 2px solid var(--primary-blue);
    }
    .mobile-find-buttons .btn-gigs:hover {
        background: transparent;
        color: var(--primary-blue);
    }
    /* Mobile auth buttons in hamburger menu */
    .mobile-auth-buttons {
        display: flex;
        flex-direction: row;
        gap: 1rem;
        margin-top: 0.5rem;
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }
    .mobile-auth-buttons .btn {
        flex: 1;
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
        border-radius: 8px;
        text-align: center;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    .mobile-auth-buttons .btn-outline {
        background: transparent;
        color: var(--primary-blue);
        border: 2px solid var(--primary-blue);
    }
    .mobile-auth-buttons .btn-outline:hover {
        background: var(--primary-blue);
        color: white;
    }
    .mobile-auth-buttons .btn-primary {
        background: var(--primary-pink);
        color: white;
        border: 2px solid var(--primary-pink);
    }
    .mobile-auth-buttons .btn-primary:hover {
        background: transparent;
        color: var(--primary-pink);
    }
    /* Mobile Responsiveness */
    @media screen and (max-width: 1200px) {
        .hamburger {
            display: block;
        }
        .nav-links {
            display: none;
        }
        .search-container {
            display: none;
        }
        .mobile-search-icon {
            display: block;
        }
        .auth-buttons {
            display: none;
        }
        .hamburger.active span:nth-child(1) {
            transform: rotate(45deg) translate(5px, 5px);
        }
        .hamburger.active span:nth-child(2) {
            opacity: 0;
        }
        .hamburger.active span:nth-child(3) {
            transform: rotate(-45deg) translate(7px, -7px);
        }
    }
    /* Footer Styles */
    footer {
        background: var(--primary-blue);
        padding: 2.5rem 5%;
        align-items: center;
        padding-bottom: 2rem;
        margin-top: 60px;
    }
    .footer-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 3rem;
        margin-bottom: 2rem;
    }
    .footer-column h3 {
        font-size: 1.3rem;
        margin-bottom: 1rem;
        color: var(--text-light);
    }
    .footer-column a {
        font-size: 1.1rem;
        display: block;
        color: var(--text-light);
        text-decoration: none;
        margin-bottom: 0.5rem;
        transition: text-decoration 0.3s ease;
    }
    .footer-column a:hover {
        text-decoration: underline;
    }
    .footer-bottom {
        font-size: 1.1rem;
        color: var(--text-light);
        text-align: center;
        padding-top: 2rem;
        border-top: 1px solid white;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10rem;
    }
    .footer-bottom a {
        color: var(--text-light);
        margin: 0 10px;
        text-decoration: none;
        white-space: nowrap;
    }
    .footer-bottom a:hover {
        text-decoration: underline;
    }
    .footer-bottom .social-icons img {
        width: 1rem;
        height: 1rem;
    }
    .social-icons .bi {
        font-size: 1.5rem;
        margin: 0 0.5rem;
        color: var(--text-light);
        text-decoration: none;
    }
    /* Mobile Footer Styles */
    @media screen and (max-width: 1200px) {
        footer {
            padding: 2rem 1rem;
        }
        .footer-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .footer-column {
            margin-bottom: 1rem;
            border-bottom: 2px solid rgba(255, 255, 255, 0.2);
            padding-bottom: 1rem;
        }
        .footer-column h3 {
            font-size: 1.5rem;
            margin-bottom: 0.8rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-weight: 600;
            gap: 0.5rem;
        }
        .footer-column h3::after {
            content: '▼';
            font-size: 1rem;
            transition: transform 0.3s ease;
            margin-left: auto;
        }
        .footer-column h3.active::after {
            transform: rotate(180deg);
        }
        .footer-column .footer-links {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        .footer-column .footer-links.show {
            max-height: 500px;
        }
        .footer-column a {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            display: block;
        }
        .footer-bottom {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
            border-top: none;
            padding: 0;
        }
        .footer-bottom p {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }
        .social-icons {
            display: flex;
            justify-content: center;
            margin-top: 0.5rem;
        }
        .footer-bottom .social-icons img {
            width: 1.5rem;
            height: 1.5rem;
            background: white;
            border-radius: 50%;
            padding: 0.25rem;
        }
    }
    .social-icons .bi {
        font-size: 1.5rem;
        margin-right: 10px;
        border-radius: 50%;
        color: white;
        transition: transform 0.3s ease, color 0.3s ease;
    }
    .social-icons .bi:hover {
        transform: scale(1.2);
        color: var(--primary-pink);
    }
    .login-modal-content {
        max-width: 400px;
        padding: 2rem;
    }
    .login-container {
        width: 100%;
    }
    .login-container h2 {
        text-align: center;
        color: var(--primary-blue);
        margin-bottom: 1.5rem;
    }
    .login-container form .btn-primary {
        width: auto;
    }
    .form-group {
        position: relative;
        margin-bottom: 1rem;
    }
    .form-group i {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: #666;
    }
    .form-group input {
        width: 100%;
        padding: 0.8rem 1rem 0.8rem 2.5rem;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 1rem;
    }
    .checkbox-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }
    .forgot-password-link {
        color: var(--primary-blue);
        text-decoration: none;
        font-size: 0.9rem;
        white-space: nowrap;
    }
    .forgot-password-link:hover {
        text-decoration: underline;
    }
    .or-separator {
        text-align: center;
        margin: 1rem 0;
        position: relative;
    }
    .or-separator::before,
    .or-separator::after {
        content: '';
        position: absolute;
        top: 50%;
        width: 45%;
        height: 1px;
        background-color: #ddd;
    }
    .or-separator::before {
        left: 0;
    }
    .or-separator::after {
        right: 0;
    }
    .signup-link {
        text-align: center;
        margin-top: 1rem;
        font-size: 0.9rem;
    }
    .signup-link a {
        color: var(--primary-blue);
        text-decoration: none;
        font-weight: 500;
    }
    .signup-link a:hover {
        text-decoration: underline;
    }
    .forgot-password-modal {
        max-width: 400px;
        padding: 2rem;
    }
    .forgot-password-modal h2 {
        color: var(--primary-blue);
        margin-bottom: 1rem;
    }
    .forgot-password-modal input {
        width: 100%;
        padding: 0.8rem;
        margin: 1rem 0;
        border: 1px solid #ddd;
        border-radius: 5px;
    }
    .social-login-buttons {
        margin: 1.5rem 0;
        width: 100%;
        padding-right: 20px;
    }
    .social-login-btn {
        width: 100%;
        height: 45px;
        border-radius: 15px;
        border: 1px solid #ddd;
        background: white;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    .social-login-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
    .social-login-btn:active {
        transform: translateY(-1px);
    }
    .social-login-btn i {
        font-size: 1.3rem;
        transition: all 0.3s ease;
        margin-right: 10px;
    }
    .social-login-btn span {
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    .social-login-btn .bi-google {
        color: #DB4437;
    }
    .social-login-btn:hover .bi {
        transform: scale(1.2);
    }
    .social-login-btn::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.6);
        border-radius: 50%;
        transform: scale(0);
        transition: transform 0.3s ease-out;
    }
    .social-login-btn:active::after {
        transform: scale(2);
        opacity: 0;
    }
    .social-login-btn {
        animation: slideInRight 0.5s ease forwards;
        opacity: 0;
    }
    .social-login-btn:nth-child(1) { animation-delay: 0.1s; }
    .social-login-btn:nth-child(2) { animation-delay: 0.2s; }
    .social-login-btn:nth-child(3) { animation-delay: 0.3s; }
    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(20px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    .security-code-modal {
        max-width: 450px;
        padding: 2rem;
        text-align: center;
    }
    .security-code-modal h2 {
        color: var(--primary-blue);
        margin-bottom: 1rem;
    }
    .email-display {
        background-color: #f5f5f5;
        padding: 0.8rem;
        margin: 1rem 0;
        border-radius: 5px;
        font-weight: 500;
        word-break: break-all;
    }
    .security-code-inputs {
        display: flex;
        justify-content: center;
        gap: 8px;
        margin: 1.5rem 0;
    }
    .code-input {
        width: 40px;
        height: 50px;
        text-align: center;
        font-size: 1.5rem;
        border: 1px solid #ddd;
        border-radius: 5px;
    }
    .code-input:focus {
        border-color: var(--primary-blue);
        outline: none;
    }
    .resend-code {
        margin-top: 1rem;
        font-size: 0.9rem;
    }
    .resend-code a {
        color: var(--primary-blue);
        text-decoration: none;
    }
    .resend-code a:hover {
        text-decoration: underline;
    }
    #verificationModal .modal-content {
        max-width: 500px;
    }
    #verificationMessage {
        margin: 20px 0;
        font-size: 1.1rem;
        text-align: center;
        line-height: 1.5;
    }
    /* Hero Section with Background Image */
    .hero-section {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1rem;
        max-width: 1400px;
        margin: -80px auto 0;
        text-align: center;
        padding: 80px 10% 60px;
        background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('../static/img/phhi12.jpg');
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
        border-radius: 0 0 15px 15px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    .hero-content {
        max-width: 800px;
        margin: 0 auto;
    }
    .hero-content h1 {
        font-size: 3rem;
        color: var(--text-light);
        margin-bottom: 1.5rem;
        line-height: 1.2;
    }
    .hero-content p {
        font-size: 1.5rem;
        color: var(--text-light);
        margin-bottom: 2.5rem;
    }
    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 40px 20px;
    }
    h1 {
        text-align: center;
        font-size: 2.5rem;
        color: var(--primary-blue);
        margin-bottom: 40px;
        font-weight: 700;
    }
    .tabs {
        display: flex;
        justify-content: center;
        margin-bottom: 50px;
        flex-wrap: wrap;
    }
    .tab {
        padding: 12px 24px;
        border-radius: 30px;
        cursor: pointer;
        font-weight: 500;
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    .tab.active {
        background-color: var(--primary-blue);
        border: 2px solid var(--primary-pink);
        color: white;
    }
    .tab:not(.active) {
        background-color: transparent;
        border: 2px solid var(--primary-pink);
        color: var(--yellow);
        border: 1px solid #ddd;
    }
    .tab-content {
        display: none;
    }
    .tab-content.active {
        display: block;
    }
    .steps-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
        margin-top: 30px;
    }
    .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
    .step-icon {
        width: 120px;
        height: 120px;
        margin-bottom: 20px;
    }
    .step-number {
        background: var(--primary-pink);
        color: white;
        padding: 12px 20px;
        border-radius: 25px;
        display: inline-block;
        font-weight: 700;
        font-size: 1.2rem;
        margin-bottom: 15px;
        box-shadow: 0 2px 8px rgba(255, 20, 147, 0.3);
        text-align: center;
    }
    .step-title {
        font-weight: 700;
        font-size: 1.2rem;
        margin-bottom: 10px;
        display: none;
    }
    .step-description {
        color: var(--text-dark);
        max-width: 300px;
        margin: 0 auto;
    }
    .section-title {
        text-align: center;
        margin-bottom: 2rem;
        font-size: 2rem;
        color: var(--primary-blue);
    }
    .talent-categories {
        padding: 80px 5% 40px;
        margin-top: 60px;
    }
    .category-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 2rem;
        max-width: 1400px;
        margin: 0 auto;
    }
    .category-card {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        text-align: center;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
    }
    .category-card:hover {
        transform: translateY(-5px);
    }
    .category-card i {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        padding: 20px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        transition: all 0.3s ease;
    }
    /* Individual icon colors */
    .category-card:nth-child(1) i { /* Development & IT */
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }
    .category-card:nth-child(2) i { /* AI Services */
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
    }
    .category-card:nth-child(3) i { /* Design & Creative */
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
    }
    .category-card:nth-child(4) i { /* Sales & Marketing */
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        box-shadow: 0 4px 15px rgba(67, 233, 123, 0.3);
    }
    .category-card:nth-child(5) i { /* Admin & Customer Support */
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        box-shadow: 0 4px 15px rgba(250, 112, 154, 0.3);
    }
    .category-card:nth-child(6) i { /* Writing & Translation */
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        box-shadow: 0 4px 15px rgba(168, 237, 234, 0.3);
        color: #333;
    }
    .category-card:nth-child(7) i { /* Finance & Accounting */
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        box-shadow: 0 4px 15px rgba(255, 236, 210, 0.3);
        color: #333;
    }
    .category-card:nth-child(8) i { /* HR & Training */
        background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
        box-shadow: 0 4px 15px rgba(161, 140, 209, 0.3);
    }
    .category-card:nth-child(9) i { /* Legal */
        background: linear-gradient(135deg, #fad0c4 0%, #ffd1ff 100%);
        box-shadow: 0 4px 15px rgba(250, 208, 196, 0.3);
        color: #333;
    }
    .category-card:nth-child(10) i { /* Engineering & Architecture */
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        box-shadow: 0 4px 15px rgba(255, 154, 158, 0.3);
        color: #333;
    }
    .category-card:hover i {
        transform: scale(1.1);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    }
    .category-card h3 {
        color: var(--primary-blue);
        font-size: 1.25rem;
        margin-bottom: 0.5rem;
        font-weight: 700;
    }
    .category-card p {
        color: #666;
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
        height: 40px;
    }
    .button-container {
        display: flex;
        justify-content: center;
        width: 100%;
        margin-top: 20px;
        margin-bottom: 30px;
    }
    .create-account-btn {
        margin: 0 auto;
    }
    
    /* Mobile-specific text fixes */
    @media screen and (max-width: 768px) {
        .hero-section {
            padding: 80px 5% 40px;
            background-attachment: scroll;
        }
        
        .hero-content h1 {
            font-size: 2.2rem;
            line-height: 1.3;
            padding: 0 1rem;
        }
        
        .hero-content p {
            font-size: 1.2rem;
            line-height: 1.5;
            padding: 0 1rem;
            margin-bottom: 2rem;
        }
        
        h1 {
            font-size: 2rem;
            padding: 0 1rem;
        }
        
        .section-title {
            font-size: 1.8rem;
            padding: 0 1rem;
        }
        
        .category-card h3 {
            font-size: 1.1rem;
        }
        
        .category-card p {
            font-size: 0.9rem;
            height: auto;
        }
        
        .step-description {
            font-size: 1rem;
            max-width: 100%;
            padding: 0 1rem;
        }
        
        .tab {
            font-size: 0.9rem;
            padding: 10px 16px;
            margin: 0.25rem;
        }
        
        .button-container {
            margin-bottom: 20px;
        }
    }
    
    @media screen and (max-width: 480px) {
        .hero-section {
            padding: 80px 5% 30px;
        }
        
        .hero-content h1 {
            font-size: 1.8rem;
            line-height: 1.2;
        }
        
        .hero-content p {
            font-size: 1.1rem;
            line-height: 1.4;
            margin-bottom: 1.5rem;
        }
        
        h1 {
            font-size: 1.8rem;
        }
        
        .section-title {
            font-size: 1.6rem;
        }
        
        .category-card h3 {
            font-size: 1rem;
        }
        
        .category-card p {
            font-size: 0.85rem;
        }
        
        .step-number {
            font-size: 1rem;
            padding: 10px 16px;
        }
        
        .step-description {
            font-size: 0.95rem;
        }
        
        .tab {
            font-size: 0.85rem;
            padding: 8px 14px;
        }
        
        .button-container {
            margin-bottom: 15px;
        }
    }
</style>
</head>
    <div id="joinModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2>Join as Genius or Client</h2>
            <div id="roleMessage" style="color: red; display: none;">Please select a role!</div>
            <div class="role-selection">
                <div class="role-option" onclick="selectOption('genius')">
                    <input type="radio" name="role" id="geniusRole">
                    <label for="geniusRole">
                        <i class="bi bi-person"></i> I'm a Genius (Freelancer)
                    </label>
                </div>
                <div class="role-option" onclick="selectOption('client')">
                    <input type="radio" name="role" id="clientRole">
                    <label for="clientRole">
                        <i class="bi bi-briefcase"></i> I'm a Client (Business Owner)
                    </label>
                </div>
            </div>
            <div class="modal-buttons">
                <button onclick="continueToRegistration()" class="btn btn-primary">Continue</button>
            </div>
            <p class="login-link">
                Already have an account? <a href="javascript:void(0)" onclick="closeModal(); openLoginModal()">Log In</a>
            </p>
        </div>
    </div>

    <div id="verificationModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeVerificationModal()">&times;</span>
            <h2>Confirm Your Selection</h2>
            <div id="verificationMessage"></div>
            <div class="modal-buttons">
                <button onclick="proceedToRegistration()" class="btn btn-primary">Proceed</button>
                <button onclick="closeVerificationModal()" class="btn btn-outline">Go Back</button>
            </div>
        </div>
    </div>

    <div id="loginModal" class="modal">
        <div class="modal-content login-modal-content">
            <span class="close" onclick="closeLoginModal()">&times;</span>
            <div class="login-container">
                <h2>Login to GigGenius</h2>
                <div id="loginErrorMessage" style="color: red; margin-bottom: 1rem; display: none;"></div>
                <form id="loginForm" method="POST" action="{{ url_for('login') }}">
                    <div class="form-group">
                        <i class="bi bi-envelope"></i>
                        <input type="email" name="email" placeholder="Email" required>
                    </div>
                    <div class="form-group">
                        <i class="bi bi-lock"></i>
                        <input type="password" name="password" id="password" placeholder="Password" required>
                    </div>
                    <div class="checkbox-container">
                        <label>
                            <input type="checkbox" id="showPassword" onclick="togglePasswordVisibility()"> Show Password
                        </label>
                        <a href="javascript:void(0)" class="forgot-password-link" onclick="openForgotPasswordModal()">Forgot Password?</a>
                    </div>
                    <button type="submit" class="btn btn-primary">LOGIN</button>
                </form>
                <div class="or-separator">or</div>
                <button class="btn btn-outline" onclick="signInWithGoogle()">
                    <img src="{{ url_for('static', filename='img/lp3.png') }}" alt="Google" style="width: 20px; height: 20px; margin-right: 10px;">
                    Continue with Google
                </button>
                <div class="signup-link">
                    Don't have a GigGenius account? <a href="javascript:void(0)" onclick="closeLoginModal(); openModal();">Sign Up</a>
                </div>
            </div>
        </div>
    </div>

    <div id="forgotPasswordModal" class="modal">
        <div class="modal-content forgot-password-modal">
            <span class="close" onclick="closeForgotPasswordModal()">&times;</span>
            <h2>Forgot Password</h2>
            <p>Please enter your email address to reset your password.</p>
            <input type="email" id="forgotPasswordEmail" placeholder="Email" required>
            <button class="btn btn-primary" onclick="submitForgotPassword()">Submit</button>
        </div>
    </div>

    <div id="securityCodeModal" class="modal">
        <div class="modal-content security-code-modal">
            <span class="close" onclick="closeSecurityCodeModal()">&times;</span>
            <div class="text-center mb-4">
                <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo" style="width: 80px; height: 80px; border-radius: 50%;">
            </div>
            <h2>Verify Security Code</h2>
            <p>We'll send a security code to your email for verification.</p>
            <div class="email-display" id="securityCodeEmail"></div>
            <div class="code-input-container" style="display: none;" id="codeInputContainer">
                <p>Enter the 6-digit code sent to your email:</p>
                <div class="security-code-inputs">
                    <input type="text" maxlength="1" class="code-input" data-index="1">
                    <input type="text" maxlength="1" class="code-input" data-index="2">
                    <input type="text" maxlength="1" class="code-input" data-index="3">
                    <input type="text" maxlength="1" class="code-input" data-index="4">
                    <input type="text" maxlength="1" class="code-input" data-index="5">
                    <input type="text" maxlength="1" class="code-input" data-index="6">
                </div>
                <div id="codeErrorMessage" style="color: red; margin-top: 0.5rem; display: none;"></div>
                <button class="btn btn-primary" onclick="verifySecurityCode()">Verify</button>
                <p class="resend-code">
                    Didn't receive the code? <a href="javascript:void(0)" onclick="resendSecurityCode()">Resend Code</a>
                </p>
            </div>
            <button class="btn btn-primary" id="sendCodeBtn" onclick="sendSecurityCode()">Send Code</button>
        </div>
    </div>

<body>
    <!-- Side Navigation -->
    <div class="side-nav" id="sideNav">
        <div class="side-nav-content">
            <div class="nav-items">
                <a href="{{ url_for('landing_page') }}" class="nav-item">Home</a>
                <a href="{{ url_for('find_geniuses') }}" class="nav-item">Find Geniuses</a>
                <a href="{{ url_for('find_gigs') }}" class="nav-item">Find Gigs</a>
                <a href="{{ url_for('about_us') }}" class="nav-item">About Us</a>
                <div class="mobile-auth-buttons">
                    <a href="javascript:void(0)" onclick="openLoginModal(); toggleMenu();" class="btn btn-outline">Log In</a>
                    <a href="javascript:void(0)" onclick="openModal(); toggleMenu();" class="btn btn-primary">Join</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Side nav overlay -->
    <div class="side-nav-overlay" id="sideNavOverlay"></div>

    <!-- Mobile Search Overlay -->
    <div class="search-overlay" id="searchOverlay">
        <div class="expanded-search-container">
            <div class="expanded-search-header">
                <h3>Search</h3>
                <button class="close-search-btn" onclick="closeMobileSearch()">&times;</button>
            </div>
            <div class="expanded-search-form">
                <div class="expanded-search-type">
                    <div class="search-type-chip active" data-type="all">All</div>
                    <div class="search-type-chip" data-type="genius">Genius</div>
                    <div class="search-type-chip" data-type="gigs">Gigs</div>
                    <div class="search-type-chip" data-type="client">Client</div>
                </div>
                <div class="expanded-search-input">
                    <input type="text" id="mobileSearchInput" placeholder="Search...">
                    <button class="search-btn" onclick="performMobileSearch()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <nav class="navbar">
        <div style="display: flex; align-items: center;">
            <button class="hamburger" onclick="toggleMenu()">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <a href="{{ url_for('landing_page') }}" style="text-decoration: none;">
                <div class="logo">
                    <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                    
                </div>
            </a>
            <div class="nav-links">
                <a href="{{ url_for('landing_page') }}">Home</a>
                <a href="{{ url_for('find_geniuses') }}">Find Geniuses</a>
                <a href="{{ url_for('find_gigs') }}" class="active">Find Gigs</a>
                <a href="{{ url_for('about_us') }}">About Us</a>
            </div>
        </div>

        <div class="right-section">
            <div class="search-container">
                <div class="search-type-select">
                    <button class="search-type-button" id="searchTypeBtn">
                        <span id="selectedSearchType">All</span>
                    </button>
                    <div class="search-type-dropdown" id="searchTypeDropdown">
                        <div class="search-type-option" data-value="all">All</div>
                        <div class="search-type-option" data-value="genius">Genius</div>
                        <div class="search-type-option" data-value="gigs">Gigs</div>
                        <div class="search-type-option" data-value="client">Client</div>
                    </div>
                </div>
                <div class="search-bar">
                    <input type="text" id="searchInput" placeholder="Search...">
                    <i class="fas fa-search icon"></i>
                </div>
            </div>
            <button class="mobile-search-icon" onclick="openMobileSearch()">
                <i class="fas fa-search"></i>
            </button>
            <div class="auth-buttons">
                <a href="javascript:void(0)" onclick="openLoginModal()" class="btn btn-outline">Log In</a>
                <a href="javascript:void(0)" onclick="openModal()" class="btn btn-primary">Join</a>
            </div>
        </div>
    </nav>

    <section class="hero-section">
        <div class="hero-content">
            <h1>Work the way you want</h1>
            <p>Find the right gig for you, with great clients, at the world’s gig marketplace.</p>
            <button class="btn btn-gigs" onclick="openModal()">Join</button>
        </div>
    </section>

    <section class="container">
        <h1>Explore the different ways to earn</h1>

        <div class="tabs">
            <button class="tab active" data-tab="talent-marketplace">Genius Marketplace</button>
            <button class="tab" data-tab="project-catalog">Project Catalog</button>
        </div>

        <div id="talent-marketplace" class="tab-content active">
            <div class="steps-container">
                <div class="step">
                    <img src="{{ url_for('static', filename='img/f_gigs1.png') }}" alt="Create a profile icon" class="step-icon">
                    <p class="step-number">1. Create a profile</p>
                    <h3 class="step-title">Create a profile</h3>
                    <p class="step-description">Highlight your skills and experience, show your portfolio, and set your ideal pay rate.</p>
                </div>

                <div class="step">
                    <img src="{{ url_for('static', filename='img/f_gigs2.png') }}" alt="Search for jobs icon" class="step-icon">
                    <p class="step-number">2. Search for jobs</p>
                    <h3 class="step-title">Search for jobs</h3>
                    <p class="step-description">Search on Talent Marketplace™ for the hourly or fixed-price work you're looking for.</p>
                </div>

                <div class="step">
                    <img src="{{ url_for('static', filename='img/f_gigs3.png') }}" alt="Submit a proposal icon" class="step-icon">
                    <p class="step-number">3. Submit a proposal</p>
                    <h3 class="step-title">Submit a proposal</h3>
                    <p class="step-description">Set your rate and tell clients why you're the right person for the job!</p>
                </div>

                <div class="step">
                    <img src="{{ url_for('static', filename='img/f_gigs4.png') }}" alt="Get contract icon" class="step-icon">
                    <p class="step-number">4. Get contract</p>
                    <h3 class="step-title">Get contract</h3>
                    <p class="step-description">If the client likes your proposal they'll send you a contract to begin working.</p>
                </div>

                <div class="step">
                    <img src="{{ url_for('static', filename='img/f_gigs5.png') }}" alt="Complete the work icon" class="step-icon">
                    <p class="step-number">5. Complete the work</p>
                    <h3 class="step-title">Complete the work</h3>
                    <p class="step-description">Check steps off as you finish and work with your client if you have questions.</p>
                </div>

                <div class="step">
                    <img src="{{ url_for('static', filename='img/f_gigs6.png') }}" alt="Get paid securely icon" class="step-icon">
                    <p class="step-number">6. Get paid securely</p>
                    <h3 class="step-title">Get paid securely</h3>
                    <p class="step-description">Once the client approves your work, you'll get paid and they can leave you feedback.</p>
                </div>
            </div>
        </div>

        <div id="project-catalog" class="tab-content">
            <div class="steps-container">
                <div class="step">
                    <img src="{{ url_for('static', filename='img/f_gigs7.png') }}" alt="Create a project icon" class="step-icon">
                    <p class="step-number">1. Create a project</p>
                    <h3 class="step-title">Create a project</h3>
                    <p class="step-description">Create a unique project that showcases your expertise.</p>
                </div>

                <div class="step">
                    <img src="{{ url_for('static', filename='img/f_gigs8.png') }}" alt="Project is reviewed icon" class="step-icon">
                    <p class="step-number">2. Project is reviewed</p>
                    <h3 class="step-title">Project is reviewed</h3>
                    <p class="step-description">We'll let you know if you need to make any changes before it's visible to clients.</p>
                </div>

                <div class="step">
                    <img src="{{ url_for('static', filename='img/f_gigs9.png') }}" alt="Get an order icon" class="step-icon">
                    <p class="step-number">3. Get an order</p>
                    <h3 class="step-title">Get an order</h3>
                    <p class="step-description">Your timeline starts once the client provides the info you need.</p>
                </div>

                <div class="step">
                    <img src="{{ url_for('static', filename='img/f_gigs10.png') }}" alt="Complete the work icon" class="step-icon">
                    <p class="step-number">4. Complete the work</p>
                    <h3 class="step-title">Complete the work</h3>
                    <p class="step-description">Check steps off as you finish and work with your client if you have questions.</p>
                </div>

                <div class="step">
                    <img src="{{ url_for('static', filename='img/f_gigs11.png') }}" alt="Get paid securely icon" class="step-icon">
                    <p class="step-number">5. Get paid securely</p>
                    <h3 class="step-title">Get paid securely</h3>
                    <p class="step-description">Once the client approves your work, you'll get paid and they can leave you feedback.</p>
                </div>
            </div>
        </div>
    </section>

    <section class="talent-categories">
        <h2 class="section-title">Gigs that's waiting for you</h2>
        <div class="category-grid">
            <div class="category-card">
                <i class="fas fa-code"></i>
                <h3>Development & IT</h3>
                <p>5k+ jobs posted weekly</p>
            </div>

            <div class="category-card">
                <i class="fas fa-robot"></i>
                <h3>AI Services</h3>
                <p>5k+ jobs posted weekly</p>
            </div>

            <div class="category-card">
                <i class="fas fa-paint-brush"></i>
                <h3>Design & Creative</h3>
                <p>5k+ jobs posted weekly</p>
            </div>

            <div class="category-card">
                <i class="fas fa-chart-line"></i>
                <h3>Sales & Marketing</h3>
                <p>5k+ jobs posted weekly</p>
            </div>

            <div class="category-card">
                <i class="fas fa-headset"></i>
                <h3>Admin & Customer Support</h3>
                <p>5k+ jobs posted weekly</p>
            </div>

            <div class="category-card">
                <i class="fas fa-pen"></i>
                <h3>Writing & Translation</h3>
                <p>5k+ jobs posted weekly</p>
            </div>

            <div class="category-card">
                <i class="fas fa-chart-line"></i>
                <h3>Finance & Accounting</h3>
                <p>5k+ jobs posted weekly</p>
            </div>

            <div class="category-card">
                <i class="fas fa-users"></i>
                <h3>HR & Training</h3>
                <p>5k+ jobs posted weekly</p>
            </div>

            <div class="category-card">
                <i class="fas fa-balance-scale"></i>
                <h3>Legal</h3>
                <p>5k+ jobs posted weekly</p>
            </div>

            <div class="category-card">
                <i class="fas fa-drafting-compass"></i>
                <h3>Engineering & Architecture</h3>
                <p>5k+ jobs posted weekly</p>
            </div>
        </div>
        <div class="button-container">
            <button class="btn btn-primary create-account-btn" onclick="openModal()">Create account</button>
        </div>
    </section>

    <footer>
        <div class="footer-grid">
            <div class="footer-column">
                <h3>For Clients</h3>
                <div class="footer-links">
                    <a href="{{ url_for('how_to_hire') }}">How to Hire</a>
                    <a href="{{ url_for('accounting_services') }}">Accounting Services</a>
                    <a href="{{ url_for('events') }}">Events & Collaborations</a>
                    <a href="{{ url_for('ph_business_loan') }}">PH Business Loan</a>
                </div>
            </div>
            <div class="footer-column">
                <h3>For Geniuses</h3>
                <div class="footer-links">
                    <a href="{{ url_for('how_it_works') }}">How It Works?</a>
                    <a href="{{ url_for('why_cant_apply') }}">About Rejected Accounts</a>
                    <a href="{{ url_for('find_mentors') }}">GigGenius University</a>
                    <a href="{{ url_for('ph_health_insurance') }}">PH Health Insurance</a>
                    <a href="{{ url_for('ph_life_insurance') }}">PH Life Insurance</a>
                </div>
            </div>
            <div class="footer-column">
                <h3>Resources</h3>
                <div class="footer-links">
                    <a href="{{ url_for('help_and_support') }}">Help & Support</a>
                    <a href="{{ url_for('news_and_events') }}">News & Events</a>
                    <a href="{{ url_for('affiliate_program') }}">Affiliate Program</a>
                </div>
            </div>
            <div class="footer-column">
                <h3>Company</h3>
                <div class="footer-links">
                    <a href="{{ url_for('about_us') }}">About Us</a>
                    <a href="{{ url_for('contact_us') }}">Contact Us</a>
                    <a href="{{ url_for('charity_projects') }}">Charity Projects</a>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>Follow Us:
                <span class="social-icons">
                    <a href="https://www.facebook.com/giggenius.io" class="bi bi-facebook"></a>
                    <a href="https://www.instagram.com/giggenius.io/" class="bi bi-instagram"></a>
                    <a href="https://twitter.com/giggenius_io" class="bi bi-twitter-x"></a>
                    <a href="https://www.tiktok.com/@giggenius.io" class="bi bi-tiktok"></a>
                    <a href="https://www.youtube.com/@giggenius" class="bi bi-youtube"></a>
                    <a href="https://www.linkedin.com/company/gig-genius/" class="bi bi-linkedin"></a>
                </span>
            </p>
            <p>©2025 GigGenius by<a href="https://genuinelysolutions.com/">Genuinely Business Solutions</a></p>
            <p>
                <a href="{{ url_for('terms_of_service') }}">Terms of Service</a> |
                <a href="{{ url_for('privacy_policy') }}">Privacy Policy</a>
            </p>
        </div>
    </footer>

    <script>
        // Hamburger Menu Functions
        function toggleMenu() {
            const sideNav = document.getElementById('sideNav');
            const overlay = document.getElementById('sideNavOverlay');
            const hamburger = document.querySelector('.hamburger');
            const spans = hamburger.getElementsByTagName('span');
            const body = document.body;

            sideNav.classList.toggle('active');
            overlay.classList.toggle('active');

            // Prevent body scroll when menu is open
            if (sideNav.classList.contains('active')) {
                body.classList.add('menu-open');
                // Animate hamburger to X
                spans[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
                spans[1].style.opacity = '0';
                spans[2].style.transform = 'rotate(-45deg) translate(7px, -7px)';
            } else {
                body.classList.remove('menu-open');
                // Reset hamburger
                spans[0].style.transform = 'none';
                spans[1].style.opacity = '1';
                spans[2].style.transform = 'none';
            }
        }

        // Close side nav when clicking outside
        document.addEventListener('click', (e) => {
            const sideNav = document.getElementById('sideNav');
            const hamburger = document.querySelector('.hamburger');

            if (sideNav.classList.contains('active') &&
                !sideNav.contains(e.target) &&
                !hamburger.contains(e.target)) {
                toggleMenu();
            }
        });

        // Mobile Search Functions
        function openMobileSearch() {
            const searchOverlay = document.getElementById('searchOverlay');
            const body = document.body;

            searchOverlay.classList.add('active');
            body.classList.add('menu-open');

            // Focus on search input after animation
            setTimeout(() => {
                document.getElementById('mobileSearchInput').focus();
            }, 300);
        }

        function closeMobileSearch() {
            const searchOverlay = document.getElementById('searchOverlay');
            const body = document.body;

            searchOverlay.classList.remove('active');
            body.classList.remove('menu-open');
        }

        function performMobileSearch() {
            const searchInput = document.getElementById('mobileSearchInput');
            const searchTerm = searchInput.value.trim();
            const activeChip = document.querySelector('.search-type-chip.active');
            const searchType = activeChip ? activeChip.dataset.type : 'all';

            if (searchTerm) {
                // Perform search logic here
                console.log('Searching for:', searchTerm, 'Type:', searchType);
                // You can add your search logic here

                // Close the search overlay
                closeMobileSearch();

                // Clear the input
                searchInput.value = '';
            }
        }

        // Handle search type chip selection
        document.addEventListener('DOMContentLoaded', function() {
            const searchChips = document.querySelectorAll('.search-type-chip');
            const mobileSearchInput = document.getElementById('mobileSearchInput');

            searchChips.forEach(chip => {
                chip.addEventListener('click', function() {
                    // Remove active class from all chips
                    searchChips.forEach(c => c.classList.remove('active'));
                    // Add active class to clicked chip
                    this.classList.add('active');

                    // Update placeholder based on selection
                    const placeholders = {
                        genius: 'Search for freelancers...',
                        gigs: 'Search for gigs...',
                        client: 'Search for clients...',
                        all: 'Search...'
                    };
                    const type = this.dataset.type;
                    mobileSearchInput.placeholder = placeholders[type] || placeholders.all;
                });
            });

            // Handle Enter key in mobile search
            mobileSearchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performMobileSearch();
                }
            });

            // Close search overlay when clicking outside
            document.getElementById('searchOverlay').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeMobileSearch();
                }
            });
        });
        let selectedRole = null;

        function closeAllModals() {
            // Get all elements with class "modal" and hide them
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                modal.style.display = 'none';
            });
            // Restore scrolling
            document.body.style.overflow = 'auto';
        }

        function openModal() {
            closeAllModals(); // Close any open modals first
            document.getElementById('joinModal').style.display = 'flex';
            document.body.style.overflow = 'hidden'; // Prevent background scrolling
        }

        function closeModal() {
            document.getElementById('joinModal').style.display = 'none';
            document.body.style.overflow = 'auto'; // Restore scrolling
            document.getElementById('roleMessage').style.display = 'none';
            document.querySelectorAll('.role-option').forEach(option => {
                option.classList.remove('selected');
            });
            document.getElementById('geniusRole').checked = false;
            document.getElementById('clientRole').checked = false;
        }

        function selectOption(role) {
            document.querySelectorAll('.role-option').forEach(option => {
                option.classList.remove('selected');
            });

            event.currentTarget.classList.add('selected');
            document.getElementById('roleMessage').style.display = 'none';

            if (role === 'genius') {
                document.getElementById('geniusRole').checked = true;
                document.getElementById('clientRole').checked = false;
            } else {
                document.getElementById('clientRole').checked = true;
                document.getElementById('geniusRole').checked = false;
            }

        }

        function continueToRegistration() {
            showVerificationModal();
        }

        function openLoginModal() {
            closeAllModals(); // Close any open modals first
            document.getElementById('loginModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        function closeLoginModal() {
            document.getElementById('loginModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function openForgotPasswordModal() {
            closeAllModals(); // Close any open modals first
            document.getElementById('forgotPasswordModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        function closeForgotPasswordModal() {
            document.getElementById('forgotPasswordModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function togglePasswordVisibility() {
            const passwordInput = document.getElementById('password');
            passwordInput.type = passwordInput.type === 'password' ? 'text' : 'password';
        }

        function submitForgotPassword() {
            const email = document.getElementById('forgotPasswordEmail').value;
            // Add your forgot password logic here
            closeForgotPasswordModal();
        }

        window.onclick = function(event) {
            const joinModal = document.getElementById('joinModal');
            const loginModal = document.getElementById('loginModal');
            const forgotPasswordModal = document.getElementById('forgotPasswordModal');

            if (event.target === joinModal) {
                closeModal();
            }
            if (event.target === loginModal) {
                closeLoginModal();
            }
            if (event.target === forgotPasswordModal) {
                closeForgotPasswordModal();
            }
        }

        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        document.addEventListener('DOMContentLoaded', function() {
        const searchTypeBtn = document.getElementById('searchTypeBtn');
        const searchTypeDropdown = document.getElementById('searchTypeDropdown');
        const selectedSearchType = document.getElementById('selectedSearchType');
        const searchInput = document.getElementById('searchInput');
        const options = document.querySelectorAll('.search-type-option');

        // Toggle dropdown
        searchTypeBtn.addEventListener('click', function() {
            searchTypeDropdown.classList.toggle('active');
        });

        // Handle option selection
        options.forEach(option => {
            option.addEventListener('click', function() {
                const value = this.dataset.value;
                selectedSearchType.textContent = this.textContent;
                searchTypeDropdown.classList.remove('active');

                // Update placeholder based on selection
                const placeholders = {
                    genius: 'Search for freelancers...',
                    gigs: 'Search for gigs...',
                    client: 'Search for clients...',
                    all: 'Search...'
                };
                searchInput.placeholder = placeholders[value] || placeholders.all;
            });
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.search-type-select')) {
                searchTypeDropdown.classList.remove('active');
            }
        });
        });
    document.getElementById('loginForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        fetch("{{ url_for('login') }}", {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.requireVerification) {
                    // Show security code verification modal for admin
                    openSecurityCodeModal();
                    // Display the email in the verification modal
                    document.getElementById('securityCodeEmail').textContent = data.email;
                    // Set a flag to indicate this is admin verification
                    window.selectedRole = null; // Clear any role selection
                    window.isAdminVerification = true;
                } else {
                    // Regular user redirect
                    window.location.href = data.redirect;
                }
            } else {
                // Show error message
                const errorMessage = document.getElementById('loginErrorMessage');
                errorMessage.textContent = data.error;
                errorMessage.style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            const errorMessage = document.getElementById('loginErrorMessage');
            errorMessage.textContent = "An error occurred during login. Please try again.";
            errorMessage.style.display = 'block';
        });
    });

    function openSecurityCodeModal() {
        closeAllModals(); // Close any open modals first
        document.getElementById('securityCodeModal').style.display = 'flex';
        document.body.style.overflow = 'hidden';
        document.getElementById('codeInputContainer').style.display = 'none';
        document.getElementById('sendCodeBtn').style.display = 'block';
    }

    function closeSecurityCodeModal() {
        document.getElementById('securityCodeModal').style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    function sendSecurityCode() {
        // Skip actual code sending and just show the code input
        document.getElementById('sendCodeBtn').style.display = 'none';
        document.getElementById('codeInputContainer').style.display = 'block';

        // Focus on first input
        document.querySelector('.code-input[data-index="1"]').focus();

        // Add event listeners for code inputs
        setupCodeInputs();
    }

    function setupCodeInputs() {
        const inputs = document.querySelectorAll('.code-input');

        inputs.forEach((input, index) => {
            input.addEventListener('keyup', function(e) {
                // If a number is entered
                if (/^[0-9]$/.test(e.key)) {
                    // Move to next input if available
                    if (index < inputs.length - 1) {
                        inputs[index + 1].focus();
                    }
                }
                // Handle backspace
                else if (e.key === 'Backspace') {
                    // Move to previous input if available and current is empty
                    if (index > 0 && input.value === '') {
                        inputs[index - 1].focus();
                    }
                }
            });

            // Handle paste event
            input.addEventListener('paste', function(e) {
                e.preventDefault();
                const pastedData = e.clipboardData.getData('text');
                if (/^\d+$/.test(pastedData)) {
                    // Fill inputs with pasted digits
                    for (let i = 0; i < Math.min(pastedData.length, inputs.length); i++) {
                        inputs[i].value = pastedData[i];
                    }
                    // Focus on the next empty input or the last one
                    const nextEmptyIndex = Array.from(inputs).findIndex(input => !input.value);
                    if (nextEmptyIndex !== -1) {
                        inputs[nextEmptyIndex].focus();
                    } else {
                        inputs[inputs.length - 1].focus();
                    }
                }
            });
        });
    }

    function verifySecurityCode() {
        // Get email from the form
        let email;
        const emailInput = document.getElementById('registrationEmail');
        if (emailInput) {
            email = emailInput.value;
        } else {
            email = document.getElementById('securityCodeEmail').textContent;
        }

        // Get the code from inputs
        const codeInputs = document.querySelectorAll('.code-input');
        let securityCode = '';
        codeInputs.forEach(input => {
            securityCode += input.value || '0'; // Use '0' for any empty input
        });

        // Create form data for verification
        const formData = new FormData();
        formData.append('email', email);
        formData.append('code', securityCode);

        // Determine which endpoint to use based on context
        const isAdminVerification = window.isAdminVerification === true;
        const endpoint = isAdminVerification ? "{{ url_for('verify_admin') }}" : "{{ url_for('verify_email') }}";

        // Send verification request to server
        fetch(endpoint, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (isAdminVerification) {
                    // Admin verification successful - redirect to admin page
                    window.location.href = data.redirect;
                } else {
                    // Registration verification - proceed to registration
                    closeSecurityCodeModal();
                    // Redirect to appropriate registration page
                    if (window.selectedRole === 'genius') {
                        window.location.href = "{{ url_for('genius_registration') }}";
                    } else {
                        window.location.href = "{{ url_for('client_registration') }}";
                    }
                }
            } else {
                // For testing purposes, proceed anyway
                if (isAdminVerification) {
                    window.location.href = "{{ url_for('admin_page') }}";
                } else {
                    closeSecurityCodeModal();
                    if (window.selectedRole === 'genius') {
                        window.location.href = "{{ url_for('genius_registration') }}";
                    } else {
                        window.location.href = "{{ url_for('client_registration') }}";
                    }
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            // For testing purposes, proceed anyway
            if (window.isAdminVerification) {
                window.location.href = "{{ url_for('admin_page') }}";
            } else {
                closeSecurityCodeModal();
                if (window.selectedRole === 'genius') {
                    window.location.href = "{{ url_for('genius_registration') }}";
                } else {
                    window.location.href = "{{ url_for('client_registration') }}";
                }
            }
        });
    }

    function resendSecurityCode() {
        // Reset the code inputs
        document.querySelectorAll('.code-input').forEach(input => {
            input.value = '';
        });

        // Hide error message if shown
        document.getElementById('codeErrorMessage').style.display = 'none';

        // Show sending state
        const resendLink = document.querySelector('.resend-code a');
        const originalText = resendLink.textContent;
        resendLink.textContent = 'Sending...';
        resendLink.style.pointerEvents = 'none';

        setTimeout(() => {
            // Restore link text
            resendLink.textContent = originalText;
            resendLink.style.pointerEvents = 'auto';

            // Focus on first input
            document.querySelector('.code-input[data-index="1"]').focus();
        }, 1500);
    }

    function closeVerificationModal() {
        document.getElementById('verificationModal').style.display = 'none';
    }

    function showVerificationModal() {
        const geniusRole = document.getElementById('geniusRole').checked;
        const clientRole = document.getElementById('clientRole').checked;
        const roleMessage = document.getElementById('roleMessage');

        if (!geniusRole && !clientRole) {
            roleMessage.style.display = 'block';
            return;
        }

        roleMessage.style.display = 'none';
        const verificationMessage = document.getElementById('verificationMessage');

        if (geniusRole) {
            verificationMessage.innerHTML = 'You are about to register as a <strong>Genius (Freelancer)</strong>. Is this correct?';
        } else if (clientRole) {
            verificationMessage.innerHTML = 'You are about to register as a <strong>Client (Business Owner)</strong>. Is this correct?';
        }

        document.getElementById('joinModal').style.display = 'none';
        document.getElementById('verificationModal').style.display = 'flex';
    }

    function proceedToRegistration() {
        const geniusRole = document.getElementById('geniusRole').checked;
        const clientRole = document.getElementById('clientRole').checked;

        // Show security code verification before proceeding to registration
        if (geniusRole || clientRole) {
            // Store the selected role in a variable for later use
            window.selectedRole = geniusRole ? 'genius' : 'client';

            // Close verification modal and open security code modal
            document.getElementById('verificationModal').style.display = 'none';
            openRegistrationSecurityModal();
        }
    }

    function openRegistrationSecurityModal() {
        closeAllModals(); // Close any open modals first
        document.getElementById('securityCodeModal').style.display = 'flex';
        document.body.style.overflow = 'hidden';
        document.getElementById('codeInputContainer').style.display = 'none';
        document.getElementById('sendCodeBtn').style.display = 'block';

        // Update modal title and description for registration context
        document.querySelector('#securityCodeModal h2').textContent = 'Verify Your Email';
        document.querySelector('#securityCodeModal p').textContent = 'We\'ll send a security code to verify your email before registration.';

        // Get email from the join modal if available
        const emailInput = document.querySelector('#joinModal input[type="email"]');
        if (emailInput && emailInput.value) {
            document.getElementById('securityCodeEmail').textContent = emailInput.value;
        } else {
            // If no email is available, show an input field
            document.getElementById('securityCodeEmail').innerHTML = '<input type="email" id="registrationEmail" placeholder="Enter your email" required>';
        }
    }
    document.addEventListener('DOMContentLoaded', function() {
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');

            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // Remove active class from all tabs
                    tabs.forEach(t => t.classList.remove('active'));
                    // Add active class to clicked tab
                    this.classList.add('active');

                    // Hide all tab contents
                    tabContents.forEach(content => content.classList.remove('active'));
                    // Show the corresponding tab content
                    const tabId = this.getAttribute('data-tab');
                    document.getElementById(tabId).classList.add('active');
                });
            });
        });

        // Footer collapsible functionality for mobile
        document.addEventListener('DOMContentLoaded', function() {
            const footerHeadings = document.querySelectorAll('.footer-column h3');

            footerHeadings.forEach(heading => {
                heading.addEventListener('click', function() {
                    // Only work on mobile screens
                    if (window.innerWidth <= 1200) {
                        // Toggle active class on heading
                        this.classList.toggle('active');

                        // Get the next sibling element (the links container)
                        const linksContainer = this.nextElementSibling;

                        // Toggle the show class
                        if (linksContainer && linksContainer.classList.contains('footer-links')) {
                            linksContainer.classList.toggle('show');
                        }
                    }
                });
            });
        });
    </script>
</body>
</html>
