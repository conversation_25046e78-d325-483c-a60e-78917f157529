<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GigGenius | The Freelance Marketplace </title>
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='img/logo.png') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>

        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --yellow: #FFD700;
            --text-dark: #000000;
            --text-light: #FFFFFF;
            --text-gray: #666;
        }

        html {
            font-size: 1rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            transition: all 0.3s ease-in-out;
        }

        body {
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        /* Button Styles */
        .auth-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
            position: relative;
        }

        .btn {
            padding: 0 1.5rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 120px;
            height: 50px;
        }

        .btn-primary {
            background: var(--primary-pink);
            border: 2px solid var(--primary-pink);
            color: var(--text-light);
        }

        .btn-outline {
            background: white;
            border: 2px solid var(--primary-blue);
            color: var(--primary-blue);
        }

        .btn-gigs {
            background: var(--primary-blue);
            border: 2px solid var(--primary-blue);
            color: var(--text-light);
        }

        .btn-primary:hover {
            background: white;
            border: 2px solid var(--primary-pink);
            color: var(--primary-pink);
            text-decoration: none;
        }

        .btn-outline:hover {
            background: var(--primary-blue);
            color: var(--text-light);
            text-decoration: none;
        }

        .btn-gigs:hover {
            background: white;
            color: var(--primary-blue);
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.4);
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: var(--text-light);
            padding: 2rem;
            border-radius: 10px;
            width: 90%;
            max-width: 500px;
            position: relative;
            text-align: center;
        }

        .modal-content h2 {
            font-size: 24px;
            color: var(--primary-blue);
        }

        .modal-buttons {
            margin-top: 1.5rem;
            display: flex;
            justify-content: center;
            padding: 0 2rem; 
        }

        .modal-buttons .btn {
            width: auto;
            min-width: 160px;
            max-width: 80%;
            margin: 0 auto;
        }

        .social-login-btn {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            margin-bottom: 10px;
        }

        .social-login-btn img {
            width: 20px;
            height: 20px;
            object-fit: contain;
        }

        .close {
            position: absolute;
            right: 1rem;
            top: 0.1rem;
            font-size: 2rem;
            cursor: pointer;
        }

        .role-selection {
            margin: 2rem 0;
        }

        .role-option {
            border: 2px solid var(--primary-blue);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .role-option:hover {
            background-color: var(--text-light);
        }

        .role-option.selected {
            border-color: var(--primary-pink);
            background-color: var(--text-light);
        }

        .role-option input[type="radio"] {
            margin-right: 0.5rem;
        }

        .login-link {
            text-align: center;
            margin-top: 15px;
            font-size: 0.9rem;
            color: var(--text-gray);
        }

        .login-link a {
            color: var(--primary-blue);
            text-decoration: none;
            font-weight: 500;
        }

        .login-link a:hover {
            text-decoration: underline;
        }

        .login-modal-content {
            max-width: 400px;
            padding: 2rem;
        }

        .login-container {
            width: 100%;
        }

        .login-container h2 {
            text-align: center;
            color: var(--primary-blue);
            margin-bottom: 1.5rem;
        }

        .login-container form .btn-primary {
            width: auto;
            position: relative;
            min-width: 120px;
            transition: all 0.3s ease;
        }

        /* Login Button Loading States */
        .login-container form .btn-primary:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }

        .login-spinner {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .login-spinner i {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .login-text, .login-spinner {
            transition: all 0.3s ease;
        }

        .form-group {
            position: relative;
            margin-bottom: 1rem;
        }

        .form-group i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-gray);
        }

        .form-group input {
            width: 100%;
            padding: 0.8rem 1rem 0.8rem 2.5rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }

        .checkbox-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .forgot-password-link {
            color: var(--primary-blue);
            text-decoration: none;
            font-size: 0.9rem;
        }

        .forgot-password-link:hover {
            text-decoration: underline;
        }

        .or-separator {
            text-align: center;
            margin: 1rem 0;
            position: relative;
        }

        .or-separator::before,
        .or-separator::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 45%;
            height: 1px;
            background-color: #ddd;
        }

        .or-separator::before {
            left: 0;
        }

        .or-separator::after {
            right: 0;
        }

        .signup-link {
            text-align: center;
            margin-top: 15px;
            font-size: 0.9rem;
            color: var(--text-gray);
        }

        .signup-link a {
            color: var(--primary-blue);
            text-decoration: none;
            font-weight: 500;
        }

        .signup-link a:hover {
            text-decoration: underline;
        }

        .forgot-password-modal {
            max-width: 400px;
            padding: 2rem;
        }

        .forgot-password-modal h2 {
            color: var(--primary-blue);
            margin-bottom: 1rem;
        }

        .forgot-password-modal input {
            width: 100%;
            padding: 0.8rem;
            margin: 1rem 0;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        /* Container Styles */
        .container {
            max-width: 2000px;
            margin: auto;
            padding: auto;
        }

        /* Navbar Styles */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0rem 1rem;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 5.5rem;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 1.7rem;
            color: var(--primary-pink);
            margin-right: 1rem;
        }

        .logo img {
            width: 5rem;
            height: 5rem;
        }

        .logo h1 {
            font-size: 1.7rem;
            margin-left: -0.5rem;
            font-weight: bold;
        }

        .nav-links {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-links a {
            color: var(--primary-blue);
            text-decoration: none;
            padding: 0.5rem 1rem;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .nav-links a:hover, .nav-links a.active {
            color: var(--primary-pink);
        }

        .navbar-right {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .search-auth-content {
            display: flex;
            align-items: center;
            gap: 0;
            padding: 0.5rem 1rem;
        }

        .search-type-select {
            position: relative;
        }

        .search-type-button {
            height: 50px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0 1rem;
            border: 2px solid var(--primary-blue);
            border-radius: 8px 0 0 8px;
            border-right: none;
            background: white;
            cursor: pointer;
            font-size: 1rem;
            color: var(--primary-blue);
        }

        .search-type-button:hover {
            color: var(--primary-pink);
            border-color: var(--primary-pink);
        }

        .search-type-button:after {
            content: '▼';
            font-size: 1rem;
        }

        .search-type-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            background: white;
            border: 2px solid var(--primary-blue);
            border-radius: 8px;
            margin-top: 0.5rem;
            display: none;
            z-index: 1000;
            width: max-content;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .search-type-dropdown.active {
            display: block;
        }

        .search-type-option {
            padding: 1rem;
            cursor: pointer;
            color: var(--primary-blue);
        }

        .search-type-option:hover {
            color: var(--primary-pink);
        }

        .search-bar {
            height: 50px;
            display: flex;
            align-items: center;
            background: white;
            border: 2px solid var(--primary-blue);
            border-radius: 0 8px 8px 0;
            width: 200px;
            margin-right: 1rem;
        }

        .search-bar:hover {
            border-color: var(--primary-pink);
        }

        .search-type-button:hover + .search-bar {
            border-color: var(--primary-pink);
        }

        .search-bar input {
            border: none;
            outline: none;
            padding: 0 1rem;
            width: 100%;
            height: 100%;
            font-size: 1rem;
        }

        .search-bar input:focus {
            outline: none;
        }

        .search-bar .icon {
            color: var(--primary-blue);
            padding: 0 0.5rem;
            font-size: 1rem;  
        }

        .search-bar:hover .icon {
            color: var(--primary-pink);
        }

        /* Section1 Styles */
        .section1 {
            display: grid;
            grid-template-columns: 1.5fr 1fr;
            align-items: center;
            padding: 8rem 0;
            position: relative;
            overflow: hidden;
            background-image: url("{{ url_for('static', filename='img/lp1.jpg') }}");
            background-size: cover;
        }

        .section1 .content {
            padding-left: 3%;
            padding-right: 3%;
            position: relative;
        }

        .section1 .content h1 {
            font-size: 4.5rem;
            color: var(--primary-blue);
            line-height: 1.1;
            margin-bottom: 1rem;
        }

        .section1 .content h2 {
            font-size: 4rem;
            color: var(--primary-pink);
            line-height: 1.1;
            margin-bottom: 1rem;
        }

        .section1 .content p {
            font-size: 2rem;
            margin-right: 15rem;
            margin-bottom: 5rem;
        }

        .highlight-pink {
            color: var(--primary-pink);
            font-weight: bold;
        }

        .highlight-blue {
            color: var(--primary-blue);
            font-weight: bold;
        }

        .section1 .btn {
            height: 60px;      
            font-size: 1.2rem;  
            min-width: 200px;  
            font-weight: 600;   
        }

        /* Section2 Styles */
        .section2 {
            background: var(--primary-blue);
            color: var(--text-light);
            text-align: center;
            padding: 3rem 5%;
            align-items: center;
        }

        .section2 .content {
            display: flex;
            flex-direction: column;  
            align-items: center;  
        }

        .section2 h1 {
            color: white;
            font-size: 3.5rem;
            line-height: 1.6;
            margin-bottom: 2rem;
        }

        .section2 p {
            color: white;
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }

        .section2 .highlight {
            background: var(--yellow);
            color: var(--text-dark);
            padding: 0.2rem 1rem;
            border-radius: 4px;
        }

        .section2 .btn {
            height: 60px;      
            font-size: 1.2rem;  
            min-width: 150px;  
            font-weight: 600;   
        }

        /* Section3 Styles */
        .section3 {
            background: var(--text-light);
            text-align: center;
            padding: 5rem 5%;
            align-items: center;
        }

        .section3 {
            background: var(--text-light);
            color: var(--text-light);
            text-align: center;
            padding: 3rem 5%;
            align-items: center;
        }

        .section3 .content {
            margin: 0 auto;
            padding: 0 2rem;
        }

        .section3 .title-container {
            width: 100%;
        }

        .section3 h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            color: var(--primary-blue);
            text-align: center;
            width: 100%;
        }

        .section3 .subtitle {
            font-size: 2rem;
            color: var(--text-dark);
            margin-bottom: 3rem;
            text-align: center;
            width: 100%;
        }

        .section3 .cards-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 2rem;
        }

        .section3 .benefit-card {
            flex: 1;
            min-width: 250px;
            max-width: calc(25% - 2rem);
            margin: 0;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 8px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            margin-bottom: 1.5rem;
        }

        .section3 .benefit-card:hover {
            transform: translateY(-5px);
        }

        .section3 .benefit-card i {
            font-size: 3rem;
            color: var(--primary-blue);
            margin-bottom: 1rem;
        }

        .section3 .benefit-card h3 {
            color: var(--primary-pink);
            margin-bottom: 1rem;
            font-size: 1.6rem;
        }

        .section3 .benefit-card p {
            color: var(--text-dark);
            line-height: 1.6;
            font-size: 1.1rem;
        }

        /* Section4 Styles */
        .section4 {
            background: var(--primary-pink);
            text-align: center;
            padding: 3rem 5%;
            align-items: center;
        }

        .section4 .content {
            margin: 0 auto;
        }

        .section4 h1 {
            text-align: center;
            font-size: 3.5rem;
            color: var(--text-light);
            margin-bottom: 1rem;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(2, minmax(320px, 700px));
            gap: 1.5rem;
            justify-content: center;
        }

        .feature {
            background: white;
            padding: 2.5rem;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .feature h2 {
            color: var(--primary-blue);
            font-size: 3rem;
        }

        .feature h3 {
            color: var(--text-dark);
            font-size: 1.5rem;
            margin-bottom: 2rem;
            font-weight: normal;
        }

        .feature ul {
            list-style: none;
            padding: 0;
        }

        .feature ul li {
            margin-bottom: 1rem;
            padding-left: 3.5rem;
            position: relative;
            color: var(--text-dark);
            font-size: 1.2rem;
            text-align: left;
        }

        .feature ul li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
            background-color: var(--yellow);
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: -0.2rem;
        }

        /* Section5 Styles */
        .section5 {
            background: white;
            color: white;
            padding: 0;
            display: grid;
            grid-template-columns: 1fr 1fr;
            align-items: center;
            min-height: 600px;
            text-align: left;
        }

        .section5 .content {
            padding: 2rem 5%;
        }

        .section5 h1 {
            color: var(--primary-blue);
            text-align: left;
            margin-bottom: 0.2rem;
            font-size: 4.5rem;
            position: relative;
            padding-bottom: 0.2rem;
        }

        .section5 h2 {
            color: var(--primary-blue);
            text-align: left;
            margin: 0 0 1rem 0;
            font-size: 2.3rem;
        }

        .section5 p {
            color: var(--text-dark);
            text-align: left; 
            margin-bottom: 1rem;
            font-size: 1.5rem;
            line-height: 1.4;
            padding-right: 2rem; 
        }

        .section5 .points {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem; 
            margin: 2rem 0; 
            position: relative;
        }

        .section5 .points .card {
            position: relative;
            padding-left: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .section5 .points .card p {
            margin-bottom: 0.5rem;
            font-size: 1.4rem;
            padding-right: 1rem;
        }

        .section5 .points .card::before {
            content: '•';
            position: absolute;
            left: 0;
            font-size: 2.5rem;
            color: var(--primary-pink);
            top: 0;
            line-height: 1;
        }

        .section5 .image-container {
            width: 100%;
            height: 100%;
        }

        .section5 .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .section5 .btn {
            height: 60px;      
            font-size: 1.2rem;  
            min-width: 200px;  
            font-weight: 600;   
        }

        .section5 .auth-buttons {
            display: flex;
            justify-content: flex-start;
            margin-top: 2rem;
            gap: 1rem;
        }

        /* Section6 Styles */
        .section6 {
            background: white;
            color: white;
            padding: 0;
            display: grid;
            grid-template-columns: 1fr 1fr;
            align-items: center;
            min-height: 600px;
        }

        .section6 .content {
            padding: 2rem 5%;
        }

        .section6 h1 {
            color: var(--primary-pink);
            text-align: left;
            margin-bottom: 0.2rem;
            font-size: 3.5rem;
            position: relative;
            padding-bottom: 0.2rem;
        }

        .section5 h2 {
            color: var(--primary-pink);
            text-align: left;
            margin: 0.5rem 0 1rem 0;
            font-size: 2.3rem;
        }

        .section6 h2 {
            color: var(--primary-blue);
            text-align: left;
            margin: 0.5rem 0 1rem 0;
            font-size: 2.3rem;
        }

        .section6 p {
            color: var(--text-dark);
            text-align: left; 
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
            line-height: 1.4;
            padding-right: 2rem;
        }

        .section6 .points {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
            margin: 2rem 0;
            position: relative;
        }

        .section6 .points .card {
            position: relative;
            padding-left: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .section6 .points .card p {
            margin-bottom: 0.5rem;
            font-size: 1.4rem;
            padding-right: 1rem;
        }

        .section6 .points .card::before {
            content: '•';
            position: absolute;
            left: 0;
            font-size: 2.5rem;
            color: var(--primary-blue);
            bottom: -0.5rem;
        }

        .section6 .image-container {
            width: 100%;
            height: 100%;
        }

        .section6 .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .section6 .btn {
            height: 60px;      
            font-size: 1.2rem;  
            min-width: 200px;  
            font-weight: 600;   
        }

        .section6 .auth-buttons {
            display: flex;
            justify-content: flex-start;
            margin-top: 2rem;
            gap: 1rem;
        }

        /* Footer Styles */
        footer {
            background: var(--primary-blue);
            padding: 2.5rem 5%;
            align-items: center;
            padding-bottom: 2rem;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 3rem;
            margin-bottom: 2rem;
        }
        
        .footer-column h3 {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            color: var(--text-light);
        }

        .footer-column a {
            font-size: 1.1rem;
            display: block;
            color: var(--text-light);
            text-decoration: none;
            margin-bottom: 0.5rem;
            transition: text-decoration 0.3s ease; 
        }

        .footer-column a:hover {
            text-decoration: underline;
        }

        .footer-bottom {
            font-size: 1.1rem;
            color: var(--text-light);
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid white;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10rem;
        }

        .footer-bottom a {
            color: var(--text-light);
            margin: 0 10px;
            text-decoration: none;
        }

        .footer-bottom a:hover {
            text-decoration: underline;
        }

        .footer-bottom .social-icons img {
            width: 1rem;
            height: 1rem;
        }

        .social-icons .bi {
            font-size: 1.5rem; 
            margin-right: 10px;
            border-radius: 50%;
            color: var(--text-light);
            transition: transform 0.3s ease, color 0.3s ease;
        }

        .social-icons .bi:hover {
            transform: scale(1.2);
            color: var(--primary-pink);
        }

        /* Hamburger Menu Styles */
        .hamburger {
            position: relative; /* Change from fixed to relative */
            display: none;
            cursor: pointer;
            padding: 15px;
            background: none;
            border: none;
            z-index: 1001;
        }

        .hamburger span {
            display: block;
            width: 25px;
            height: 3px;
            margin: 5px 0;
            background-color: var(--primary-blue);
            transition: all 0.3s ease;
        }

        /* Side nav styles */
        .side-nav {
            position: fixed;
            top: 2rem;
            left: -100%;
            height: 100vh;
            width: 100%;
            background-color: white;
            z-index: 1000;
            transition: left 0.3s ease;
        }

        .side-nav.active {
            left: 0;
        }

        .side-nav-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0,0,0,0.5);
            z-index: 999;
            display: none;
        }

        .side-nav-overlay.active {
            display: block;
        }

        .side-nav-content {
            padding: 60px 0 20px 0;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
        }

        .nav-item {
            display: flex;  
            flex-direction: column;
            align-items: center;
            gap: 10px;
            width: 100%;
            max-width: 300px;
            padding: 20px;
            color: var(--primary-blue);
            text-decoration: none;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s ease;
            font-size: 1.2rem;
            text-align: center;
            margin: 10px 0;
            font-weight: 500;
        }

        .nav-item:hover {
            color: var(--primary-pink);
        }

        .side-nav.active + .hamburger span {
            background-color: var(--primary-blue);
        }

        /* TV */
        @media screen and (min-width: 1921px) and (max-width: 3840px) {
            .section1 .image {
                display: none;
            }
        }

        /* Desktop & Laptop */
        @media screen and (min-width: 1721px) and (max-width: 1920px) {
            .section1 .image {
                display: none;
            }
        }

        /* Medium to Large Desktops */
        @media screen and (max-width: 1720px) {
            .section1 .image {
                display: none;
            }
            .section1 .content h1 {
                font-size: 4.5rem;
                line-height: 1.2;
            }
            .section1 .content h2 {
                font-size: 4rem;
                line-height: 1.2;
            }
            .section1 .content p {
                font-size: 2rem;
                margin-right: 15rem;
            }
        }

        @media screen and (max-width: 1500px) {
            .section1 .image {
                display: none;
            }
            .section1 .content h1 {
                font-size: 4rem;
                line-height: 1.2;
            }
            .section1 .content h2 {
                font-size: 3.5rem;
                line-height: 1.2;
            }
            .section1 .content p {
                font-size: 1.8rem;
                margin-right: 12rem;
            }
            .navbar {
                position: relative;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.5rem;
                background: white;
                z-index: 1003;
            }
            .logo img {
                width: 4rem;
                height: 4rem;
            }
            .search-container,
            .search-type-select,
            .search-bar {
                display: none;
            }
        }

        /* Tablets & Small Laptops */
        @media screen and (max-width: 1200px) {
            .container {
                width: 100%;
                max-width: 100%;
                margin: 0;
                padding: 0;
                overflow-x: hidden;
            }
            .navbar {
                position: relative;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.5rem;
                background: white;
                z-index: 1003;
            }
            .hamburger {
                display: block;
            }
            .nav-links {
                display: none;
            }
            .logo h1 {
                display: none;
            }
            .logo img {
                width: 4rem;
                height: 4rem;
            }
            .search-container,
            .search-type-select,
            .search-bar {
                display: none;
            }
            .section1 {
                grid-template-columns: 1fr;
                align-items: center;
                padding: 2rem;
                position: relative;
                background-image: none;
                margin-top: -1rem;
            }
            .section1 .content {
                padding: 0;
                width: 100%;
            }
            .section1 .content h1 {
                font-size: 3.5rem;
                color: var(--primary-blue);
                line-height: 1.3;
                margin-bottom: 1rem;
                text-align: left;
            }
            .section1 .content h2 {
                font-size: 3rem;
                color: var(--primary-pink);
                line-height: 1.2;
                margin-bottom: 1rem;
                text-align: left;
            }
            .section1 .content p {
                font-size: 2rem;
                margin-bottom: 2rem;
                text-align: justify;
                margin-right: -0.1rem;
            }
            .section1 .auth-buttons {
                display: flex;
                flex-direction: column;
                justify-content: center;
                gap: 0.1rem;
                width: 100%;
            }
            .section1 .auth-buttons .btn {
                height: 55px;
                font-size: 1.1rem;
                font-weight: 500;
                max-width: 200px;
                width: 100%;
                margin: 0;
                margin-bottom: 0.8rem;
            }
            .section1 .image {
                width: 100%;
                height: 50vh;
                background-image: url("{{url_for('static', filename='img/lp6.jpg') }}");
                background-size: cover;
                background-position: center;
                margin-bottom: 1.5rem;
                border-radius: 12px;
                order: -1;
                display: block !important;
            }
        }

        /* Large Mobile Devices */
        @media screen and (max-width: 768px) {
            .container {
                width: 100%;
                max-width: 100%;
                margin: 0;
                padding: 0;
                overflow-x: hidden;
            }
            .navbar {
                position: relative;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.5rem;
                background: white;
                z-index: 1003;
            }
            .hamburger {
                display: block;
            }
            .nav-links {
                display: none;
            }
            .logo h1 {
                display: none;
            }
            .logo img {
                width: 4rem;
                height: 4rem;
            }
            .search-container,
            .search-type-select,
            .search-bar {
                display: none;
            }
            .navbar .auth-buttons {
                display: flex;
                gap: 0.6rem;
                align-items: center;
                position: relative;
            }
            .section1 {
                grid-template-columns: 1fr;
                align-items: center;
                padding: 2rem;
                position: relative;
                background-image: none;
                margin-top: -1rem;
            }
            .section1 .content {
                padding: 0;
                width: 100%;
            }
            .section1 .content h1 {
                font-size: 3rem;
                color: var(--primary-blue);
                line-height: 1.3;
                margin-bottom: 1rem;
                text-align: left;
            }
            .section1 .content h2 {
                font-size: 2.5rem;
                color: var(--primary-pink);
                line-height: 1.2;
                margin-bottom: 1rem;
                text-align: left;
            }
            .section1 .content p {
                font-size: 1.5rem;
                margin-bottom: 2rem;
                text-align: justify;
                margin-right: -0.1rem;
            }
            .section1 .auth-buttons {
                display: flex;
                flex-direction: column;
                justify-content: center;
                gap: 0.1rem;
                width: 100%;
            }
            .section1 .auth-buttons .btn {
                height: 55px;
                font-size: 1.1rem;
                font-weight: 500;
                max-width: 200px;
                width: 100%;
                margin: 0;
                margin-bottom: 0.8rem;
            }
            .section1 .image {
                width: 100%;
                height: 250px;
                background-image: url("{{url_for('static', filename='img/lp6.jpg') }}");
                background-size: cover;
                background-position: center;
                margin-bottom: 1.5rem;
                border-radius: 12px;
                order: -1;
                display: block !important;
            }
            .section2 {
                background: var(--primary-blue);
                color: var(--text-light);
                text-align: center;
                padding: 0.8rem 0;
                align-items: center;
            }
            .section2 .content {
                display: flex;
                flex-direction: column;
                align-items: center;
            }
            .section2 h1 {
                color: white;
                font-size: 2.3rem;
                line-height: 1.8;
                margin-bottom: 2rem;
                text-align: center;
            }
            .section2 p {
                color: white;
                font-size: 1.8rem;
                margin-bottom: 0.3rem;
                text-align: center;
                font-weight: 500;
                padding: 0 2.5rem;
            }
            .section2 .highlight {
                background: var(--yellow);
                color: var(--text-dark);
                padding: 0.3rem 1.2rem;
                border-radius: 5px;
            }
            .section2 .auth-buttons {
                display: flex;
                flex-direction: column;
                justify-content: center;
                gap: 0.8rem;
                width: 100%;
            }
            .section2 .auth-buttons .btn {
                height: 55px;
                font-size: 1.1rem;
                font-weight: 500;
                max-width: 200px;
                width: 100%;
                margin-top: 1.5rem;
            }
            .section3 {
                background: var(--text-light);
                text-align: center;
                padding: 2rem 1.5rem;
                align-items: center;
            }
            .section3 .content {
                display: flex;
                flex-direction: column;
                align-items: center;
                width: 100%;
            }
            .section3 h1 {
                font-size: 2.3rem;
                margin-bottom: 0.8rem;
                color: var(--primary-blue);
                text-align: center;
            }
            .section3 .subtitle {
                font-size: 1.3rem;
                margin-bottom: 1.5rem;
                text-align: center;
            }
            .section3 .cards-container {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
                width: 100%;
            }
            .section3 .benefit-card {
                width: 100%;
                background: white;
                padding: 1.5rem;
                border-radius: 12px;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
                transition: transform 0.3s ease;
                display: flex;
                flex-direction: column;
                align-items: center;
                margin: 0;
                max-width: 100%;
            }
            .section3 .benefit-card:hover {
                transform: translateY(-5px);
            }
            .section3 .benefit-card i {
                font-size: 2rem;
                color: var(--primary-blue);
                margin-bottom: 0.8rem;
            }
            .section3 .benefit-card h3 {
                color: var(--primary-pink);
                margin-bottom: 0.8rem;
                font-size: 1.3rem;
            }
            .section3 .benefit-card p {
                color: var(--text-dark);
                line-height: 1.5;
            }
            .section4 {
                background: var(--primary-pink);
                text-align: center;
                padding: 2rem 5rem;
            }
            .section4 .content {
                width: 100%;
            }
            .section4 h1 {
                font-size: 1.8rem;
                margin-bottom: 1.5rem;
            }
            .section4 .features-grid {
                display: flex;
                flex-direction: column;
                gap: 1.5rem;
                width: 100%;
            }
            .section4 .feature {
                background: white;
                padding: 1.5rem;
                border-radius: 10px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                width: 100%;
            }
            .section4 .feature h2{
                font-size: 1.8rem;
            }
            .section4 .feature h3 {
                font-size: 1.3rem;
                margin-bottom: 1.5rem;
            }
            .section4 .feature ul {
                list-style: none;
                padding: 0;
            }
            .section4 .feature ul li {
                margin-bottom: 1rem;
                padding-left: 3rem;
                position: relative;
                color: var(--text-dark);
                font-size: 1.3rem;
                text-align: left;
                line-height: 1.4;
            }
            .section4 .feature ul li::before {
                content: "✓";
                position: absolute;
                left: 0;
                color: white;
                font-weight: bold;
                font-size: 1.3rem;
                background-color: var(--yellow);
                width: 1.8rem;
                height: 1.8rem;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-top: -0.1rem;
            }
            .section5, .section6 {
                padding: 2rem 2rem;
                text-align: center;
                flex-direction: column;
                align-items: center;
                display: flex;
            }
            .section5 .image-container {
                width: 100%;
                height: auto;
                order: -1; 
            }
            .section5 .content {
                width: 100%;
                order: 1;
            }
            .section5 .image-container img {
                width: 100%;
                height: auto;
                border-radius: 8px;
            }
            .section5 h1, .section6 h1 {
                font-size: 2.5rem;
                line-height: 1.2;
            }
            .section5 h2 {
                font-size: 2rem;
                line-height: 1.2;
                text-align: justify;
                color: var(--primary-pink);
            }

            .section6 h2 {
                font-size: 2rem;
                line-height: 1.2;
                text-align: justify;
                color: var(--primary-blue);
            }
            .section5 p, .section6 p {
                font-size: 1.5rem;
                line-height: 1.2;
                text-align: left;
            }
            .section5 .btn, .section6 .btn {
                width: 100%;
                max-width: 30px;
                height: 45px;
                font-size: 1rem;
            }
            .section5 .points, .section6 .points {
                display: grid;
                grid-template-columns: 1fr;
                gap: 0.9rem;
                margin: 1.5rem 0;
            }
            .section5 .points .card, .section6 .points .card {
                position: relative;
                padding-left: 2rem;
            }
            .section5 .points .card p, .section6 .points .card p {
                font-size: 1.5rem;
            }
            .section6 .image-container {
                width: 100%;
                height: auto;
            }
            .section6 .image-container img {
                width: 100%;
                height: auto;
                border-radius: 8px;
            }
            .section5 .auth-buttons, .section6 .auth-buttons {
                display: flex;
                flex-direction: column;
                justify-content: center;
                gap: 0.8rem;
                width: 100%;
            }
            .section5 .auth-buttons .btn, .section6 .auth-buttons .btn {
                height: 55px;
                font-size: 1.1rem;
                font-weight: 500;
                max-width: 200px;
                width: 100%;
            }
            footer {
                padding: 2rem 1rem;
            }
            .footer-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
                margin-bottom: 1.5rem;
            }
            .footer-column {
                margin-bottom: 1rem;
                border-bottom: 2px solid rgba(255, 255, 255, 0.2);
                padding-bottom: 1rem;
            }
            .footer-column h3 {
                font-size: 1.5rem;
                margin-bottom: 0.8rem;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-weight: 600;
                gap: 0.5rem;
            }
            .footer-column h3::after {
                content: '▼';
                font-size: 1rem;
                transition: transform 0.3s ease;
                margin-left: auto;
            }
            .footer-column h3.active::after {
                transform: rotate(180deg);
            }
            .footer-column .footer-links {
                max-height: 0;
                overflow: hidden;
                transition: max-height 0.3s ease-out;
            }
            .footer-column .footer-links.show {
                max-height: 500px;
            }
            .footer-column a {
                font-size: 1.2rem;
                margin-bottom: 0.5rem;
                display: block;
            }
            .footer-bottom {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
                border-top: none;
                padding: 0;
            }
            .footer-bottom p {
                font-size: 1.2rem;
                margin-bottom: 0.5rem;
            }
            .social-icons {
                display: flex;
                justify-content: center;
                margin-top: 0.5rem;
            }
            .footer-bottom .social-icons img {
                width: 1.5rem;
                height: 1.5rem;
                background: white;
                border-radius: 50%;
                padding: 0.25rem;
            }
            .social-icons a {
                margin-right: 0.2rem;
            }
        }


        /* Small Mobile Devices */
        @media screen and (max-width: 480px) {
            * {
                box-sizing: border-box;
                max-width: 100%;
            }

            html, body {
                overflow-x: hidden;
                width: 100%;
                margin: 0;
                padding: 0;
            }

            /* Prevent any element from causing horizontal scroll */
            .container, .navbar, .section1, .section2, .section3, .section4, .section5, .section6, footer {
                max-width: 100vw;
                overflow-x: hidden;
            }

            .container {
                width: 100%;
                max-width: 100%;
                margin: 0;
                padding: 0;
                overflow-x: hidden;
            }

            .navbar {
                position: relative;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.5rem 1rem;
                background: white;
                z-index: 1003;
                width: 100%;
                box-sizing: border-box;
            }

            .hamburger {
                display: block;
                flex-shrink: 0;
            }

            .nav-links {
                display: none;
            }

            .logo {
                margin-left: 0;
                flex-shrink: 0;
            }

            .logo h1 {
                display: none;
            }

            .logo img {
                width: 3rem;
                height: 3rem;
            }

            .search-container,
            .search-type-select,
            .search-bar {
                display: none;
            }

            .navbar .auth-buttons {
                display: flex;
                gap: 0.5rem;
                align-items: center;
                flex-shrink: 0;
            }

            .navbar .auth-buttons .btn {
                padding: 0.4rem 0.8rem;
                font-size: 0.8rem;
                min-width: 60px;
                height: 35px;
            }
            .section1 {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 1rem;
                position: relative;
                background-image: none;
                margin-top: 0;
                width: 100%;
                box-sizing: border-box;
                overflow: hidden;
            }

            .section1 .content {
                padding: 0 1rem;
                width: 100%;
                box-sizing: border-box;
                max-width: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;
                order: 2;
            }

            .section1 .image {
                width: calc(100% - 2rem);
                height: 200px;
                background-image: url("{{url_for('static', filename='img/lp6.jpg') }}");
                background-size: cover;
                background-position: center;
                margin: 0 1rem 1.5rem 1rem;
                border-radius: 10px;
                order: 1;
                display: block !important;
                flex-shrink: 0;
            }

            .section1 .content h1 {
                font-size: 1.5rem;
                color: var(--primary-blue);
                line-height: 1.3;
                margin-bottom: 0.8rem;
                text-align: center;
                width: 100%;
                word-wrap: break-word;
                padding: 0;
            }

            .section1 .content h2 {
                font-size: 1.3rem;
                color: var(--primary-pink);
                line-height: 1.2;
                margin-bottom: 0.8rem;
                text-align: center;
                width: 100%;
                word-wrap: break-word;
                padding: 0;
            }

            .section1 .content p {
                font-size: 0.9rem;
                margin-bottom: 1.5rem;
                text-align: center;
                margin-right: 0;
                width: 100%;
                word-wrap: break-word;
                padding: 0;
                line-height: 1.4;
            }

            .section1 .auth-buttons {
                display: flex;
                flex-direction: column;
                justify-content: center;
                gap: 0.1rem;
                width: 100%;
                padding: 0;
            }

            .section1 .auth-buttons .btn {
                width: 100%;
                max-width: 200px;
                height: 40px;
                font-size: 0.8rem;
                padding: 0 0.5rem;
                margin: 0;
            }
            .section2 {
                background: var(--primary-blue);
                color: var(--text-light);
                text-align: center;
                padding: 0.5rem 1rem;
                align-items: center;
                width: 100%;
                box-sizing: border-box;
            }

            .section2 .content {
                display: flex;
                flex-direction: column;
                align-items: center;
                width: 100%;
                max-width: 1200px;
                margin: 0 auto;
            }

            .section2 h1 {
                color: white;
                font-size: 1.3rem;
                line-height: 1.4;
                margin-bottom: 0.8rem;
                text-align: center;
                width: 100%;
                padding: 0;
            }

            .section2 p {
                color: white;
                font-size: 0.9rem;
                margin-bottom: 0.2rem;
                text-align: center;
                font-weight: 400;
                padding: 0;
                width: 100%;
            }

            .section2 .highlight {
                background: var(--yellow);
                color: var(--text-dark);
                padding: 0.2rem 0.8rem;
                border-radius: 4px;
                display: inline-block;
            }

            .section2 .auth-buttons {
                display: flex;
                flex-direction: column;
                justify-content: center;
                gap: 0.5rem;
                width: 100%;
                margin-top: 0.3rem;
            }

            .section2 .auth-buttons .btn {
                height: 40px;
                font-size: 0.8rem;
                font-weight: 500;
                max-width: 200px;
                width: 100%;
                padding: 0 0.5rem;
            }
            .section3 {
                background: var(--text-light);
                text-align: center;
                padding: 2rem 1rem;
                align-items: center;
                width: 100%;
                box-sizing: border-box;
            }

            .section3 .content {
                display: flex;
                flex-direction: column;
                align-items: center;
                width: 100%;
                max-width: 100%;
            }

            .section3 h1 {
                font-size: 1.4rem;
                margin-bottom: 0.8rem;
                color: var(--primary-blue);
                text-align: center;
                width: 100%;
                line-height: 1.3;
            }

            .section3 .subtitle {
                font-size: 0.9rem;
                margin-bottom: 1.5rem;
                text-align: center;
                width: 100%;
                color: var(--text-dark);
                line-height: 1.4;
            }

            .section3 .cards-container {
                display: grid;
                grid-template-columns: 1fr;
                gap: 1rem;
                width: 100%;
                max-width: 100%;
            }

            .section3 .benefit-card {
                width: 100%;
                background: white;
                padding: 1.2rem;
                border-radius: 10px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                transition: transform 0.3s ease;
                display: flex;
                flex-direction: column;
                align-items: center;
                margin: 0;
                box-sizing: border-box;
            }

            .section3 .benefit-card:hover {
                transform: translateY(-3px);
            }

            .section3 .benefit-card i {
                font-size: 2rem;
                color: var(--primary-blue);
                margin-bottom: 0.8rem;
            }

            .section3 .benefit-card h3 {
                color: var(--primary-pink);
                margin-bottom: 0.8rem;
                font-size: 1.1rem;
                text-align: center;
            }

            .section3 .benefit-card p {
                color: var(--text-dark);
                line-height: 1.4;
                font-size: 0.9rem;
                text-align: center;
                margin: 0;
            }
            .section4 {
                background: var(--primary-pink);
                text-align: center;
                padding: 2rem 1rem;
                width: 100%;
                box-sizing: border-box;
            }

            .section4 .content {
                width: 100%;
                max-width: 100%;
            }

            .section4 h1 {
                font-size: 1.5rem;
                margin-bottom: 1.5rem;
                color: white;
                text-align: center;
            }

            .section4 .features-grid {
                display: flex;
                flex-direction: column;
                gap: 1.2rem;
                width: 100%;
            }

            .section4 .feature {
                background: white;
                padding: 1.2rem;
                border-radius: 10px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                width: 100%;
                box-sizing: border-box;
            }

            .section4 .feature h2 {
                font-size: 1.4rem;
                color: var(--primary-blue);
                margin-bottom: 0.5rem;
            }

            .section4 .feature h3 {
                font-size: 1rem;
                margin-bottom: 1rem;
                color: var(--text-dark);
            }

            .section4 .feature ul {
                list-style: none;
                padding: 0;
                margin: 0;
            }

            .section4 .feature ul li {
                margin-bottom: 0.8rem;
                padding-left: 2rem;
                position: relative;
                color: var(--text-dark);
                font-size: 0.85rem;
                text-align: left;
                line-height: 1.3;
            }

            .section4 .feature ul li::before {
                content: "✓";
                position: absolute;
                left: 0;
                color: white;
                font-weight: bold;
                font-size: 0.8rem;
                background-color: var(--yellow);
                width: 1.2rem;
                height: 1.2rem;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-top: 0.1rem;
            }
            .section5, .section6 {
                padding: 2rem 1rem;
                text-align: center;
                flex-direction: column;
                align-items: center;
                display: flex;
                width: 100%;
                box-sizing: border-box;
            }

            .section5 .image-container {
                width: 100%;
                height: auto;
                order: -1;
                margin-bottom: 1.5rem;
            }

            .section5 .content {
                width: 100%;
                order: 1;
                max-width: 100%;
            }

            .section5 .image-container img {
                width: 100%;
                height: auto;
                border-radius: 8px;
                max-width: 100%;
            }

            .section5 h1, .section6 h1 {
                font-size: 1.3rem;
                line-height: 1.3;
                margin-bottom: 1rem;
                text-align: center;
                width: 100%;
            }

            .section5 h2 {
                font-size: 1.1rem;
                line-height: 1.3;
                text-align: center;
                margin-bottom: 0.8rem;
                width: 100%;
                color: var(--primary-pink);
            }

            .section6 h2 {
                font-size: 1.1rem;
                line-height: 1.3;
                text-align: center;
                margin-bottom: 0.8rem;
                width: 100%;
                color: var(--primary-blue);
            }

            .section5 p, .section6 p {
                font-size: 0.9rem;
                line-height: 1.4;
                text-align: center;
                margin-bottom: 1rem;
                width: 100%;
            }

            .section5 .points, .section6 .points {
                display: grid;
                grid-template-columns: 1fr;
                gap: 0.8rem;
                margin: 1rem 0;
                width: 100%;
            }

            .section5 .points .card, .section6 .points .card {
                position: relative;
                padding-left: 1.8rem;
                text-align: left;
            }

            .section5 .points .card p, .section6 .points .card p {
                font-size: 0.85rem;
                line-height: 1.3;
                margin: 0;
            }

            .section5 .points .card::before, .section6 .points .card::before {
                content: '•';
                position: absolute;
                left: 0.2rem;
                font-size: 1.5rem;
                color: var(--primary-pink);
                top: -0.1rem;
                line-height: 1;
            }

            .section6 .image-container {
                width: 100%;
                height: auto;
                margin-bottom: 1.5rem;
            }

            .section6 .image-container img {
                width: 100%;
                height: auto;
                border-radius: 8px;
                max-width: 100%;
            }

            .section5 .auth-buttons, .section6 .auth-buttons {
                display: flex;
                flex-direction: column;
                justify-content: center;
                width: 100%;
                margin-top: 1.5rem;
                gap: 0.8rem;
            }

            .section5 .auth-buttons .btn, .section6 .auth-buttons .btn {
                height: 40px;
                font-size: 0.8rem;
                font-weight: 500;
                min-width: unset;
                max-width: 200px;
                flex: 1;
                margin-top: 0;
                padding: 0 0.5rem;
            }
            footer {
                padding: 2rem 1rem;
                width: 100%;
                box-sizing: border-box;
            }

            .footer-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
                margin-bottom: 1.5rem;
            }

            .footer-column {
                margin-bottom: 1rem;
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                padding-bottom: 1rem;
            }

            .footer-column h3 {
                font-size: 1.1rem;
                margin-bottom: 0.8rem;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-weight: 600;
            }

            .footer-column h3::after {
                content: '▼';
                font-size: 0.8rem;
                transition: transform 0.3s ease;
            }

            .footer-column h3.active::after {
                transform: rotate(180deg);
            }

            .footer-column .footer-links {
                max-height: 0;
                overflow: hidden;
                transition: max-height 0.3s ease-out;
            }

            .footer-column .footer-links.show {
                max-height: 500px;
            }

            .footer-column a {
                font-size: 0.9rem;
                margin-bottom: 0.5rem;
                display: block;
            }

            .footer-bottom {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
                border-top: none;
                padding: 0;
            }

            .footer-bottom p {
                font-size: 0.9rem;
                margin-bottom: 0.5rem;
            }

            .social-icons {
                display: flex;
                justify-content: center;
                margin-top: 0.5rem;
            }
            .footer-bottom .social-icons img {
                width: 1.5rem;
                height: 1.5rem;
                background: white;
                border-radius: 50%;
                padding: 0.25rem;
            }
            .social-icons a {
                margin-right: 0.2rem;
            }
            .hamburger.active span:nth-child(1) {
                transform: rotate(45deg) translate(5px, 5px);
            }

            .hamburger.active span:nth-child(2) {
                opacity: 0;
            }

            .hamburger.active span:nth-child(3) {
                transform: rotate(-45deg) translate(7px, -7px);
            }

            .modal-content,
            .login-modal-content,
            .forgot-password-modal,
            .security-code-modal {
                width: 90%;
                max-width: 350px;
                max-height: 85vh;
                overflow-y: auto;
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 10000;
                padding: 1.2rem;
                box-sizing: border-box;
            }

            .modal h2 {
                font-size: 1.3rem;
                text-align: center;
                margin-bottom: 1rem;
            }

            .modal p,
            .modal .login-container,
            .modal .signup-link,
            .modal .login-link,
            .modal .forgot-password-link,
            .modal .checkbox-container,
            .modal .or-separator {
                text-align: center;
            }

            .form-group {
                width: 100%;
                max-width: 100%;
                text-align: left;
                margin-bottom: 1rem;
            }

            .form-group input {
                width: 100%;
                padding: 0.7rem 1rem 0.7rem 2.2rem;
                font-size: 0.9rem;
                box-sizing: border-box;
            }

            .role-selection {
                width: 100%;
                text-align: center;
            }

            .role-option {
                text-align: center;
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 1rem;
                margin: 0.8rem 0;
                font-size: 0.9rem;
            }

            .social-login-btn {
                width: 100%;
                justify-content: center;
                margin: 0.8rem auto;
                font-size: 0.9rem;
            }

            .modal-buttons {
                width: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-top: 1rem;
            }

            .modal-buttons .btn {
                min-width: 120px;
                padding: 0.7rem 1rem;
                margin: 0 auto;
                font-size: 0.9rem;
            }

            .close {
                right: 0.8rem;
                top: 0.5rem;
                font-size: 1.5rem;
            }

            #loginModal,
            #joinModal {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.7);
                align-items: center;
                justify-content: center;
                z-index: 9999;
            }
        }
        /* Extra small mobile devices */
        @media screen and (max-width: 360px) {
            .navbar {
                padding: 0.3rem 0.8rem;
            }

            .navbar .auth-buttons .btn {
                padding: 0.3rem 0.6rem;
                font-size: 0.75rem;
                min-width: 55px;
                height: 32px;
            }

            .logo img {
                width: 2.5rem;
                height: 2.5rem;
            }

            .section1 {
                padding: 0.8rem;
            }

            .section1 .image {
                height: 160px;
                margin: 0 0.5rem 1.2rem 0.5rem;
            }

            .section1 .content {
                padding: 0 0.5rem;
            }

            .section1 .content h1 {
                font-size: 1.3rem;
                line-height: 1.2;
            }

            .section1 .content h2 {
                font-size: 1.1rem;
            }

            .section1 .content p {
                font-size: 0.8rem;
                line-height: 1.3;
            }

            .section1 .auth-buttons .btn {
                max-width: 110px;
                height: 36px;
                font-size: 0.75rem;
            }

            .section2 {
                padding: 0.4rem 0.8rem;
            }

            .section2 h1 {
                font-size: 1.1rem;
                line-height: 1.3;
            }

            .section2 p {
                font-size: 0.8rem;
                margin-bottom: 0.3rem;
            }

            .section2 .auth-buttons {
                margin-top: 0.3rem;
            }

            .section3 h1 {
                font-size: 1.2rem;
            }

            .section3 .subtitle {
                font-size: 0.8rem;
            }

            .section3 .benefit-card {
                padding: 1rem;
            }

            .section3 .benefit-card h3 {
                font-size: 1rem;
            }

            .section3 .benefit-card p {
                font-size: 0.8rem;
            }


        }
        .btn.btn-warning:hover {
            background-color: #6b5003 !important; /* Darker shade of the original color */
            transform: scale(1.05);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
    </style>
</head>

<!-- TATANGGALIN DIN-->
<!-- Maintenance Modal -->
<div id="maintenanceModal" class="modal" style="display: none;">
    <div class="modal-content" style="background-color: var(--text-light); border: 2px solid var(--primary-blue); max-width: 500px; max-height: 400px; border-radius: 10px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);">
        <span class="close" onclick="closeMaintenanceModal()" style="color: var(--primary-blue); font-size: 1.5rem;">&times;</span>
        <div style="text-align: center; padding: 1rem;">
            <i class="bi bi-info-circle" style="font-size: 3rem; color: var(--primary-blue); margin-bottom: 1rem;"></i>
            <h2 style="color: var(--primary-blue); font-weight: 600;">Work in Progress</h2>
            <p style="color: var(--text-gray); margin: 1rem 0;">We're still working on some pages of our website. Please bear with us as we continue to improve your experience.</p>
            <button onclick="closeMaintenanceModal()" class="btn btn-primary">Got it!</button>
        </div>
    </div>
</div>


    <!-- Modal for Login -->
    <div id="loginModal" class="modal">
        <div class="modal-content login-modal-content">
            <span class="close" onclick="closeLoginModal()">&times;</span>
            <div class="login-container">
                <h2>Login to GigGenius</h2>
                <div id="loginErrorMessage" style="color: red; margin-bottom: 1rem; display: none;"></div>
                <form id="loginForm" method="POST" action="{{ url_for('login') }}">
                    <div class="form-group">
                        <i class="bi bi-envelope"></i>
                        <input type="email" name="email" id="email" placeholder="Email" required>
                    </div>
                    <div class="form-group">
                        <i class="bi bi-lock"></i>
                        <input type="password" name="password" id="password" placeholder="Password" required>
                    </div>
                    <div class="checkbox-container">
                        <label>
                            <input type="checkbox" id="showPassword" onclick="togglePasswordVisibility()"> Show Password
                        </label>
                        <a href="javascript:void(0)" class="forgot-password-link" onclick="openForgotPasswordModal()">Forgot Password?</a>
                    </div>
                    <button type="submit" class="btn btn-primary" id="loginButton">
                        <span class="login-text">LOGIN</span>
                        <span class="login-spinner" style="display: none;">
                            <i class="fas fa-circle-notch fa-spin"></i> Logging in...
                        </span>
                    </button>
                </form>
                <div class="signup-link">
                    Don't have a GigGenius account? <a href="javascript:void(0)" onclick="closeLoginModal(); openModal();">Sign Up</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Forgot Password -->
    <div id="forgotPasswordModal" class="modal">
        <div class="modal-content forgot-password-modal">
            <span class="close" onclick="closeForgotPasswordModal()">&times;</span>
            <h2>Forgot Password</h2>
            <p>Please enter your email address to reset your password.</p>
            <input type="email" id="forgotPasswordEmail" placeholder="Email" required>
            <a href="javascript:void(0)" class="btn btn-primary" onclick="submitForgotPassword()" style="width: auto;">Submit</a>
        </div>
    </div>
    
    <!-- Modal for Join -->
    <div id="joinModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2>Join as Genius or Client</h2>
            <div id="roleMessage" style="color: red; display: none;">Please select a role!</div>
            <div class="role-selection">
                <div class="role-option" onclick="selectOption('genius')">
                    <input type="radio" name="role" id="geniusRole">
                    <label for="geniusRole">
                        <i class="bi bi-person"></i> I'm a Genius (Freelancer)
                    </label>
                </div>
                <div class="role-option" onclick="selectOption('client')">
                    <input type="radio" name="role" id="clientRole">
                    <label for="clientRole">
                        <i class="bi bi-briefcase"></i> I'm a Client (Business Owner)
                    </label>
                </div>
            </div>
            <div class="modal-buttons">
                <a href="javascript:void(0)" class="btn btn-primary" onclick="continueToRegistration()">Continue</a>
            </div>
            <p class="login-link">
                Already have an account? <a href="javascript:void(0)" onclick="closeModal(); openLoginModal()">Log In</a>
            </p>
        </div>
    </div>
<body>
    <!-- Add this side navigation panel right after the opening <body> tag -->
    <div class="side-nav" id="sideNav">
        <div class="side-nav-content">
            <div class="nav-items">
               <a href="{{ url_for('landing_page') }}" class="nav-item">Home</a>
                <a href="{{ url_for('affiliate_program') }}" class="nav-item">Affiliate Program</a>
                <a href="{{ url_for('news_and_events') }}" class="nav-item">News & Events</a>
                <a href="{{ url_for('why_giggenius') }}" class="nav-item">Why GigGenius</a>
            </div>
        </div>
    </div>
    <div class="side-nav-overlay" id="sideNavOverlay" onclick="toggleMenu()"></div>

    <!-- Main Content -->
    <div class="container">
        <!-- Navbar -->
        <nav class="navbar">
            <div style="display: flex; align-items: center;">
                <button class="hamburger" onclick="toggleMenu()">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
                <a href="{{ url_for('landing_page') }}" style="text-decoration: none;">
                    <div class="logo">
                        <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                        <h1>GigGenius</h1>
                    </div>
                </a>
                <div class="nav-links">
                    <a href="{{ url_for('landing_page') }}" class="active">Home</a>
                    <a href="{{ url_for('affiliate_program') }}">Affiliate Program</a>
                    <a href="{{ url_for('news_and_events') }}">News & Events</a>
                    <a href="{{ url_for('why_giggenius') }}">Why GigGenius</a>
                </div>
            </div>
            <div class="navbar-right">
                <div class="search-auth-content">
                    <div class="search-type-select">
                        <button class="search-type-button" id="searchTypeBtn">
                            <span id="selectedSearchType">All</span>
                        </button>
                        <div class="search-type-dropdown" id="searchTypeDropdown">
                            <div class="search-type-option" data-value="all">All</div>
                            <div class="search-type-option" data-value="genius">Geniuses</div>
                            <div class="search-type-option" data-value="gigs">Gigs</div>
                        </div>
                    </div>
                    <div class="search-bar">
                        <input type="text" id="searchInput" placeholder="Search...">
                        <i class="fas fa-search icon"></i>
                    </div>
                    <div class="auth-buttons">
                        <a href="javascript:void(0)" onclick="openLoginModal()" class="btn btn-outline">Log In</a>
                        <a href="javascript:void(0)" onclick="openModal()" class="btn btn-primary">Join</a>
                    </div>
                </div>
            </div>
        </nav>

        <br> 
        <!-- Section 1 -->
        <section class="section1">
            <div class="image"></div>
            <div class="content">
                <h1>Freelance Marketplace for GENIUSES looking for GIGS</h1>
                <h2>Your True Ally to Success!</h2>
                <p>A secured platform where <span class="highlight-blue">GENIUSES</span> can apply for ready to roll <span class="highlight-blue">GIGS</span> without the need to spend for connects or bids.</p>
                <div class="auth-buttons">
                    <a href="{{ url_for('find_geniuses') }}" class="btn btn-primary">Find Geniuses</a>
                    <a href="{{ url_for('find_gigs') }}" class="btn btn-outline">Find Gigs</a>
                </div>
            </div>
        </section>

        <!-- Section 2 -->
        <section class="section2">
            <div class="content">
                <h1>GigGenius promises <span class="highlight">5 things</span><br><span class="highlight">no other</span> freelance marketplace can</h1>
                <p>A simpler, free to use and more network driven<br>platform for all.</p>
                <div class="auth-buttons">
                    <a href="#section3" class="btn btn-primary">Explore</a>
                </div>
            </div>
        </section>

        <!-- Section 3 -->
        <section class="section3" id="section3">
            <div class="content">
                <div class="title-container">
                    <h1><span style="color: var(--primary-pink);">GigGenius </span>makes it easy and fun!</h1>
                    <p class="subtitle">Revolutionizing the Freelancing Industry with Transparency, Support, and Fair Opportunities</p>
                </div>
                <div class="cards-container">
                    <div class="benefit-card">
                        <i class="bi bi-wallet2"></i>
                        <h3>No Predatory Fees</h3>
                        <p>Keep more of what you earn. Unlike other platforms, we eliminate hidden costs and bidding wars that eat into your income.</p>
                    </div>
                    <div class="benefit-card">
                        <i class="bi bi-people"></i>
                        <h3>Charitable Community</h3>
                        <p>Join a supportive ecosystem of professionals who collaborate, share knowledge, and help each other grow.</p>
                    </div>
                    <div class="benefit-card">
                        <i class="bi bi-graph-up-arrow"></i>
                        <h3>Growth Opportunities</h3>
                        <p>Access workshops, networking events, and mentorship programs designed to enhance your skills and career.</p>
                    </div>
                    <div class="benefit-card">
                        <i class="bi bi-shield-check"></i>
                        <h3>Quality Connections</h3>
                        <p>Connect with verified clients and talented professionals, ensuring meaningful and reliable partnerships.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 4 -->
        <section class="section4">
            <div class="content">
                <h1>Platform Features</h1>
                <div class="features-grid">
                    <div class="feature">
                        <h2>For Geniuses</h2>
                        <h3>(Freelancers)</h3>
                        <ul>
                            <li>No connects or bidding need just to apply</li>
                            <li>Free profile and portfolio creation</li>
                            <li>Access to GigGenius University</li>
                            <li>Upcoming courses and workshops</li>
                            <li>Secured account creation</li>
                            <li>No fake job posts</li>
                            <li>Optional life and Health Insurance</li>
                            <li>Recurring Income as you refer</li>    
                        </ul>
                    </div>
                    <div class="feature">
                        <h2>For Clients</h2>
                        <h3>(Business Owners)</h3>
                        <ul>
                            <li>Access to verified pool of Geniuses</li>
                            <li>Networking Events for Business Owners</li>
                            <li>Simple and transparent hiring process</li>
                            <li>Direct communication with Geniuses</li>
                            <li>Quality assurance guarantees</li>
                            <li>Flexible payment options</li>
                            <li>Installment payment for starters</li>
                            <li>Upcoming Featured Business Placements</li>
                            <li>Free Account and Job Posting</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 5 -->
        <section class="section5">
            <div class="content">
                <h1>For Geniuses</h1>
                <h2>Getting a GIG shouldn't be expensive</h2>
                <p>Meet clients you're excited to work with and take your career or business to new heights.</p>
                <div class="points">
                    <div class="card card-opportunities">
                        <p>Find opportunities for every stage of your freelance career</p>
                    </div>
                    <div class="card card-control">
                        <p>Control when, where, and how you work</p>
                    </div>
                    <div class="card card-explore">
                        <p>Explore different ways to earn</p>
                    </div>
                </div>
                <div class="auth-buttons">
                    <a href="{{ url_for('find_gigs') }}" class="btn btn-gigs">Find Gigs</a>
                </div>
            </div>
            <div class="image-container">
                <img src="{{ url_for('static', filename='img/lp3.jpg') }}" alt="GigGenius Features">
            </div>
        </section>

        <!-- Section 6 -->
        <section class="section6">
            <div class="image-container">
                <img src="{{ url_for('static', filename='img/lp4.jpg') }}" alt="For Clients">
            </div>
            <div class="content">
                <h1>For Clients</h1>
                <h2>Hire top GENIUSES without breaking the bank</h2>
                <p>Connect with skilled professionals who can help grow your business to the next level.</p>
                <div class="points">
                    <div class="card">
                        <p>Access a global pool of talented professionals</p>
                    </div>
                    <div class="card">
                        <p>Flexible hiring options that fit your needs</p>
                    </div>
                    <div class="card">
                        <p>Scale your business with the right talent</p>
                    </div>
                </div>
                <div class="auth-buttons">
                    <a href="{{ url_for('find_geniuses') }}" class="btn btn-primary">Find Geniuses</a>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer>
            <div class="footer-grid">
                <div class="footer-column">
                    <h3>For Clients</h3>
                    <div class="footer-links">
                    <a href="{{ url_for('how_to_hire') }}">How to Hire</a>
                    <a href="{{ url_for('accounting_services') }}">Accounting Services</a>
                    <a href="{{ url_for('events') }}">Events & Collaborations</a>
                    <a href="{{ url_for('ph_business_loan') }}">PH Business Loan</a>
                    </div>
                </div>
                <div class="footer-column">
                    <h3>For Geniuses</h3>
                    <div class="footer-links">
                    <a href="{{ url_for('how_it_works') }}">How It Works?</a>
                    <a href="{{ url_for('why_cant_apply') }}">About Rejected Accounts</a>
                    <a href="{{ url_for('find_mentors') }}">GigGenius University</a>
                    <a href="{{ url_for('ph_health_insurance') }}">PH Health Insurance</a>
                    <a href="{{ url_for('ph_life_insurance') }}">PH Life Insurance</a>
                    </div>
                </div>
                <div class="footer-column">
                    <h3>Resources</h3>
                    <div class="footer-links">
                    <a href="{{ url_for('help_and_support') }}">Help & Support</a>
                    <a href="{{ url_for('news_and_events') }}">News & Events</a>
                    <a href="{{ url_for('affiliate_program') }}">Affiliate Program</a>
                    </div>
                </div>
                <div class="footer-column">
                    <h3>Company</h3>
                    <div class="footer-links">
                    <a href="{{ url_for('about_us') }}">About Us</a>
                    <a href="{{ url_for('contact_us') }}">Contact Us</a>
                    <a href="{{ url_for('charity_projects') }}">Charity Projects</a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>Follow Us:
                    <span class="social-icons">
                        <a href="https://www.facebook.com/giggenius.io" class="bi bi-facebook"></a>
                        <a href="https://www.instagram.com/gig.genius.io/" class="bi bi-instagram"></a>
                        <a href="https://www.threads.com/@gig.genius.io" class="bi bi-threads"></a>
                        <a href="https://twitter.com/giggenius_io" class="bi bi-twitter-x"></a>
                        <a href="https://www.tiktok.com/@giggenius.io" class="bi bi-tiktok"></a>
                        <a href="https://www.youtube.com/@giggenius" class="bi bi-youtube"></a>
                        <a href="https://www.linkedin.com/company/gig-genius/" class="bi bi-linkedin"></a>
                    </span>
                </p>
                <p>©2025 GigGenius by<a href="https://genuinelysolutions.com/">Genuinely Business Solutions</a></p>
                <p>
                    <a href="{{ url_for('terms_of_service') }}">Terms of Service</a> | 
                    <a href="{{ url_for('privacy_policy') }}">Privacy Policy</a>
                </p>
            </div>
        </footer>
    </div>

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-33SRJGWX6H"></script>
    <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-33SRJGWX6H');
    </script>
    
    <script>
        // Navbar
        function toggleMenu() {
            const sideNav = document.getElementById('sideNav');
            const overlay = document.getElementById('sideNavOverlay');
            const hamburger = document.querySelector('.hamburger');
            const spans = hamburger.getElementsByTagName('span');
            
            sideNav.classList.toggle('active');
            overlay.classList.toggle('active');
            
            // Animate hamburger
            if (sideNav.classList.contains('active')) {
                spans[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
                spans[1].style.opacity = '0';
                spans[2].style.transform = 'rotate(-45deg) translate(7px, -7px)';
                document.body.style.overflow = 'hidden'; // Prevent scrolling when menu is open
            } else {
                spans[0].style.transform = 'none';
                spans[1].style.opacity = '1';
                spans[2].style.transform = 'none';
                document.body.style.overflow = 'auto'; // Restore scrolling when menu is closed
            }
        }

        // Close side nav when clicking outside
        document.addEventListener('click', (e) => {
            const sideNav = document.getElementById('sideNav');
            const hamburger = document.querySelector('.hamburger');
            
            if (sideNav.classList.contains('active') && 
                !sideNav.contains(e.target) && 
                !hamburger.contains(e.target)) {
                toggleMenu();
            }
        });

        document.addEventListener('DOMContentLoaded', function() {
            const footerHeadings = document.querySelectorAll('.footer-column h3');
            
            footerHeadings.forEach(heading => {
                heading.addEventListener('click', function() {
                    // Toggle active class on heading
                    this.classList.toggle('active');
                    
                    // Get the next sibling element (the links container)
                    const linksContainer = this.nextElementSibling;
                    
                    // Toggle the show class
                    linksContainer.classList.toggle('show');
                });
            });
        });

        let selectedRole = null;

        // Modal
        function closeAllModals() {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                modal.style.display = 'none';
            });
            document.body.style.overflow = 'auto';
        }
        function openModal() {
            closeAllModals(); 
            document.getElementById('joinModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }
        function closeModal() {
            document.getElementById('joinModal').style.display = 'none';
            document.body.style.overflow = 'auto';
            document.getElementById('roleMessage').style.display = 'none';
            document.querySelectorAll('.role-option').forEach(option => {
                option.classList.remove('selected');
            });
            document.getElementById('geniusRole').checked = false;
            document.getElementById('clientRole').checked = false;
        }

        // Join Modal
        function selectOption(role) {
            document.querySelectorAll('.role-option').forEach(option => {
                option.classList.remove('selected');
            });

            event.currentTarget.classList.add('selected');
            document.getElementById('roleMessage').style.display = 'none';
            
            if (role === 'genius') {
                document.getElementById('geniusRole').checked = true;
                document.getElementById('clientRole').checked = false;
            } else {
                document.getElementById('clientRole').checked = true;
                document.getElementById('geniusRole').checked = false;
            }
        }

        function continueToRegistration() {
            const geniusRole = document.getElementById('geniusRole').checked;
            const clientRole = document.getElementById('clientRole').checked;
            const roleMessage = document.getElementById('roleMessage');
            
            if (!geniusRole && !clientRole) {
                roleMessage.style.display = 'block';
                return;
            }
            
            if (geniusRole) {
                window.location.href = "{{ url_for('genius_registration') }}";
            } else {
                window.location.href = "{{ url_for('client_registration') }}";
            }
        }

        // Login Modal
        function openLoginModal() {
            closeAllModals();
            document.getElementById('loginModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }
        function closeLoginModal() {
            document.getElementById('loginModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }
        function openForgotPasswordModal() {
            closeAllModals();
            document.getElementById('forgotPasswordModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }
        function closeForgotPasswordModal() {
            document.getElementById('forgotPasswordModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }
        function togglePasswordVisibility() {
            const passwordInput = document.getElementById('password');
            passwordInput.type = passwordInput.type === 'password' ? 'text' : 'password';
        }
        function submitForgotPassword() {
            const email = document.getElementById('forgotPasswordEmail').value;
            closeForgotPasswordModal();
        }

        // Security Code Modal
        window.onclick = function(event) {
            const joinModal = document.getElementById('joinModal');
            const loginModal = document.getElementById('loginModal');
            const forgotPasswordModal = document.getElementById('forgotPasswordModal');
            
            if (event.target === joinModal) {
                closeModal();
            }
            if (event.target === loginModal) {
                closeLoginModal();
            }
            if (event.target === forgotPasswordModal) {
                closeForgotPasswordModal();
            }
        }

        // Scroll to Section
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Search Type Dropdown
        document.addEventListener('DOMContentLoaded', function() {
        const searchTypeBtn = document.getElementById('searchTypeBtn');
        const searchTypeDropdown = document.getElementById('searchTypeDropdown');
        const selectedSearchType = document.getElementById('selectedSearchType');
        const searchInput = document.getElementById('searchInput');
        const options = document.querySelectorAll('.search-type-option');
        // Toggle dropdown
        searchTypeBtn.addEventListener('click', function() {
            searchTypeDropdown.classList.toggle('active');
        });
        // Handle option selection
        options.forEach(option => {
            option.addEventListener('click', function() {
                const value = this.dataset.value;
                selectedSearchType.textContent = this.textContent;
                searchTypeDropdown.classList.remove('active');
                // Update placeholder based on selection
                const placeholders = {
                    genius: 'Search for genius...',
                    gigs: 'Search for gigs...',
                    projects: 'Search for projects...',
                    all: 'Search...'
                };
                searchInput.placeholder = placeholders[value] || placeholders.all;
            });
        });
        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.search-type-select')) {
                searchTypeDropdown.classList.remove('active');
            }
        });
        });

        // Login Form
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault(); // Prevent default form submission

            // Show loading state
            const loginButton = document.getElementById('loginButton');
            const loginText = loginButton.querySelector('.login-text');
            const loginSpinner = loginButton.querySelector('.login-spinner');
            const errorMessage = document.getElementById('loginErrorMessage');

            // Hide error message
            errorMessage.style.display = 'none';

            // Show loading state
            loginText.style.display = 'none';
            loginSpinner.style.display = 'inline-flex';
            loginButton.disabled = true;

            const formData = new FormData(this);

            fetch("{{ url_for('login') }}", {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Keep loading state while redirecting with circular loading
                    loginSpinner.innerHTML = '<i class="fas fa-circle-notch fa-spin"></i> Success! Redirecting...';
                    setTimeout(() => {
                        window.location.href = data.redirect;
                    }, 500);
                } else {
                    // Reset button state and show error
                    resetLoginButton();
                    errorMessage.textContent = data.error;
                    errorMessage.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                // Reset button state and show error
                resetLoginButton();
                errorMessage.textContent = 'An error occurred during login';
                errorMessage.style.display = 'block';
            });
        });

        // Function to reset login button state
        function resetLoginButton() {
            const loginButton = document.getElementById('loginButton');
            const loginText = loginButton.querySelector('.login-text');
            const loginSpinner = loginButton.querySelector('.login-spinner');

            loginText.style.display = 'inline';
            loginSpinner.style.display = 'none';
            loginSpinner.innerHTML = '<i class="fas fa-circle-notch fa-spin"></i> Logging in...';
            loginButton.disabled = false;
        }

        //TATANGGALIN DIN
        // Force show maintenance modal immediately after page load
        window.onload = function() {
            var modal = document.getElementById('maintenanceModal');
            modal.style.display = 'flex';
            modal.style.position = 'fixed';
            modal.style.top = '0';
            modal.style.left = '0';
            modal.style.width = '100%';
            modal.style.height = '100%';
            modal.style.backgroundColor = 'rgba(0,0,0,0.5)';
            modal.style.zIndex = '9999';
        };

        function closeMaintenanceModal() {
            document.getElementById('maintenanceModal').style.display = 'none';
        }
    </script>    
</body>
</html>