<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Affiliate Program | GigGenius</title>
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='img/logo.png') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>

        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --yellow: #FFD700;
            --text-dark: #000000;
            --text-light: #FFFFFF;
            --text-gray: #666;
        }

        html {
            font-size: 1rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            transition: all 0.3s ease-in-out;
        }

        body {
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        /* Button Styles */
        .auth-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
            position: relative;
        }

        .btn {
            padding: 0 1.5rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 120px;
            height: 50px;
        }

        .btn-primary {
            background: var(--primary-pink);
            border: 2px solid var(--primary-pink);
            color: var(--text-light);
        }

        .btn-outline {
            background: white;
            border: 2px solid var(--primary-blue);
            color: var(--primary-blue);
        }

        .btn-gigs {
            background: var(--primary-blue);
            border: 2px solid var(--primary-blue);
            color: var(--text-light);
        }

        .btn-primary:hover {
            background: white;
            border: 2px solid var(--primary-pink);
            color: var(--primary-pink);
            text-decoration: none;
        }

        .btn-outline:hover {
            background: var(--primary-blue);
            color: var(--text-light);
            text-decoration: none;
        }

        .btn-gigs:hover {
            background: white;
            color: var(--primary-blue);
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.4);
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: var(--text-light);
            padding: 2rem;
            border-radius: 10px;
            width: 90%;
            max-width: 500px;
            position: relative;
            text-align: center;
        }

        .modal-content h2 {
            font-size: 24px;
            color: var(--primary-blue);
        }

        .modal-buttons {
            margin-top: 1.5rem;
            display: flex;
            justify-content: center;
            padding: 0 2rem;
        }

        .modal-buttons .btn {
            width: auto;
            min-width: 160px;
            max-width: 80%;
            margin: 0 auto;
        }

        .social-login-btn {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            margin-bottom: 10px;
        }

        .social-login-btn img {
            width: 20px;
            height: 20px;
            object-fit: contain;
        }

        .close {
            position: absolute;
            right: 1rem;
            top: 0.1rem;
            font-size: 2rem;
            cursor: pointer;
        }

        .role-selection {
            margin: 2rem 0;
        }

        .role-option {
            border: 2px solid var(--primary-blue);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .role-option:hover {
            background-color: var(--text-light);
        }

        .role-option.selected {
            border-color: var(--primary-pink);
            background-color: var(--text-light);
        }

        .role-option input[type="radio"] {
            margin-right: 0.5rem;
        }

        .login-link {
            text-align: center;
            margin-top: 15px;
            font-size: 0.9rem;
            color: var(--text-gray);
        }

        .login-link a {
            color: var(--primary-blue);
            text-decoration: none;
            font-weight: 500;
        }

        .login-link a:hover {
            text-decoration: underline;
        }

        .login-modal-content {
            max-width: 400px;
            padding: 2rem;
        }

        .login-container {
            width: 100%;
        }

        .login-container h2 {
            text-align: center;
            color: var(--primary-blue);
            margin-bottom: 1.5rem;
        }

        .login-container form .btn-primary {
            width: auto;
        }

        .form-group {
            position: relative;
            margin-bottom: 1rem;
        }

        .form-group i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-gray);
        }

        .form-group input {
            width: 100%;
            padding: 0.8rem 1rem 0.8rem 2.5rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }

        .checkbox-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .forgot-password-link {
            color: var(--primary-blue);
            text-decoration: none;
            font-size: 0.9rem;
        }

        .forgot-password-link:hover {
            text-decoration: underline;
        }

        .or-separator {
            text-align: center;
            margin: 1rem 0;
            position: relative;
        }

        .or-separator::before,
        .or-separator::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 45%;
            height: 1px;
            background-color: #ddd;
        }

        .or-separator::before {
            left: 0;
        }

        .or-separator::after {
            right: 0;
        }

        .signup-link {
            text-align: center;
            margin-top: 15px;
            font-size: 0.9rem;
            color: var(--text-gray);
        }

        .signup-link a {
            color: var(--primary-blue);
            text-decoration: none;
            font-weight: 500;
        }

        .signup-link a:hover {
            text-decoration: underline;
        }

        .forgot-password-modal {
            max-width: 400px;
            padding: 2rem;
        }

        .forgot-password-modal h2 {
            color: var(--primary-blue);
            margin-bottom: 1rem;
        }

        .forgot-password-modal input {
            width: 100%;
            padding: 0.8rem;
            margin: 1rem 0;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        /* Container Styles */
        .container {
            max-width: 2000px;
            margin: auto;
            padding: auto;
        }

        /* Navbar Styles */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0rem 1rem;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 5.5rem;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 1.7rem;
            color: var(--primary-pink);
            margin-right: 1rem;
        }

        .logo img {
            width: 5rem;
            height: 5rem;
        }

        .logo h1 {
            font-size: 1.7rem;
            margin-left: -0.5rem;
            font-weight: bold;
        }

        .nav-links {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-links a {
            color: var(--primary-blue);
            text-decoration: none;
            padding: 0.5rem 1rem;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .nav-links a:hover, .nav-links a.active {
            color: var(--primary-pink);
        }

        .navbar-right {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .search-auth-content {
            display: flex;
            align-items: center;
            gap: 0;
            padding: 0.5rem 1rem;
        }

        .search-type-select {
            position: relative;
        }

        .search-type-button {
            height: 50px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0 1rem;
            border: 2px solid var(--primary-blue);
            border-radius: 8px 0 0 8px;
            border-right: none;
            background: white;
            cursor: pointer;
            font-size: 1rem;
            color: var(--primary-blue);
        }

        .search-type-button:hover {
            color: var(--primary-pink);
            border-color: var(--primary-pink);
        }

        .search-type-button:after {
            content: '▼';
            font-size: 1rem;
        }

        .search-type-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            background: white;
            border: 2px solid var(--primary-blue);
            border-radius: 8px;
            margin-top: 0.5rem;
            display: none;
            z-index: 1000;
            width: max-content;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .search-type-dropdown.active {
            display: block;
        }

        .search-type-option {
            padding: 1rem;
            cursor: pointer;
            color: var(--primary-blue);
        }

        .search-type-option:hover {
            color: var(--primary-pink);
        }

        .search-bar {
            height: 50px;
            display: flex;
            align-items: center;
            background: white;
            border: 2px solid var(--primary-blue);
            border-radius: 0 8px 8px 0;
            width: 200px;
            margin-right: 1rem;
        }

        .search-bar:hover {
            border-color: var(--primary-pink);
        }

        .search-type-button:hover + .search-bar {
            border-color: var(--primary-pink);
        }

        .search-bar input {
            border: none;
            outline: none;
            padding: 0 1rem;
            width: 100%;
            height: 100%;
            font-size: 1rem;
        }

        .search-bar input:focus {
            outline: none;
        }

        .search-bar .icon {
            color: var(--primary-blue);
            padding: 0 0.5rem;
            font-size: 1rem;
        }

        .search-bar:hover .icon {
            color: var(--primary-pink);
        }

        /* Sidebar Styles */
        .sidebar {
            width: var(--sidebar-width);
            padding: 2rem;
            border-right: 1px solid #e0e0e0;
        }

        .sidebar-nav {
            list-style: none;
        }

        .sidebar-nav li {
            margin-bottom: 1rem;
        }

        .sidebar-nav a {
            color: var(--primary-blue);
            text-decoration: none;
            font-size: 0.95rem;
        }

        .sidebar-nav a:hover {
            color: var(--primary-pink);
        }

        .sidebar-nav .active {
            color: var(--primary-pink);
            font-weight: bold;
            border-left: 3px solid var(--primary-pink);
            padding-left: 1rem;
        }

        /* Side nav styles */
        .side-nav {
            position: fixed;
            top: 2rem;
            left: -100%;
            height: 100vh;
            width: 100%;
            background-color: white;
            z-index: 1000;
            transition: left 0.3s ease;
        }

        .side-nav.active {
            left: 0;
        }

        .side-nav-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0,0,0,0.5);
            z-index: 999;
            display: none;
        }

        .side-nav-overlay.active {
            display: block;
        }

        .side-nav-content {
            padding: 60px 0 20px 0;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            width: 100%;
            max-width: 300px;
            padding: 20px;
            color: var(--primary-blue);
            text-decoration: none;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s ease;
            font-size: 1.2rem;
            text-align: center;
            margin: 10px 0;
            font-weight: 500;
        }

        .nav-item:hover {
            color: var(--primary-pink);
        }

        .side-nav.active + .hamburger span {
            background-color: var(--primary-blue);
        }

        /* Footer Styles */
        footer {
            background: var(--primary-blue);
            padding: 2.5rem 5%;
            align-items: center;
            padding-bottom: 2rem;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 3rem;
            margin-bottom: 2rem;
        }

        .footer-column h3 {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            color: var(--text-light);
        }

        .footer-column a {
            font-size: 1.1rem;
            display: block;
            color: var(--text-light);
            text-decoration: none;
            margin-bottom: 0.5rem;
            transition: text-decoration 0.3s ease;
        }

        .footer-column a:hover {
            text-decoration: underline;
        }

        .footer-bottom {
            font-size: 1.1rem;
            color: var(--text-light);
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid white;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10rem;
        }

        .footer-bottom a {
            color: var(--text-light);
            margin: 0 10px;
            text-decoration: none;
        }

        .footer-bottom a:hover {
            text-decoration: underline;
        }

        .footer-bottom .social-icons img {
            width: 1rem;
            height: 1rem;
        }

        .social-icons .bi {
            font-size: 1.5rem;
            margin-right: 10px;
            border-radius: 50%;
            color: var(--text-light);
            transition: transform 0.3s ease, color 0.3s ease;
        }

        .social-icons .bi:hover {
            transform: scale(1.2);
            color: var(--primary-pink);
        }

        /* Hamburger Menu Styles */
        .hamburger {
            position: relative; /* Change from fixed to relative */
            display: none;
            cursor: pointer;
            padding: 15px;
            background: none;
            border: none;
            z-index: 1001;
        }

        .hamburger span {
            display: block;
            width: 25px;
            height: 3px;
            margin: 5px 0;
            background-color: var(--primary-blue);
            transition: all 0.3s ease;
        }

        .highlights {
            color: var(--primary-pink);
        }

        /* Hero Section Styles */
        .hero {
            padding: 0;
            background-color: #f7f7f7;
            overflow: hidden;
            border-radius: 15px;
            margin-bottom: 3rem;
        }

        .hero-container {
            display: flex;
            gap: 1rem;
            max-width: 1600px;
            margin: 0 auto;
            width: 100%;
            padding: 0;
            box-sizing: border-box;
        }

        .hero-content {
            flex: 0 0 50%;
            text-align: left;
            padding-right: 1rem;
            padding-left: 1rem;
            margin-left: 0;
        }

        .hero-image {
            flex: 0 0 50%;
            margin: 0;
        }

        .hero-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
        }

        .hero-title {
            margin-top: 2rem;
            font-size: 2rem;
            color: var(--primary-blue);
        }

        .hero-content h1 {
            font-size: 3rem;
            color: var(--primary-blue);
        }

        .hero-highlights {
            margin: 2rem 0;
        }

        .hero-highlights ul {
            list-style: none;
            padding: 0;
        }

        .hero-highlights li {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            font-size: 1.1rem;
            color: var(--text-dark);
        }

        .hero-highlights li i {
            color: var(--primary-blue);
            margin-right: 1rem;
            font-size: 1.2rem;
        }

        .hero-highlights li:hover {
            transform: translateX(10px);
            transition: transform 0.3s ease;
        }

        .hero-highlights li:hover i {
            color: var(--primary-pink);
            transition: color 0.3s ease;
        }

        .register-container {
            margin-top: 1.5rem;
        }

        .btn-primary {
            padding: 0.8rem 2rem;
            font-size: 1.1rem;
            font-weight: 600;
        }

        .start-note {
            margin-top: 1.5rem;
            font-size: 0.9rem;
            color: #666;
            font-style: italic;
        }

        .small-images-container {
            display: flex;
            justify-content: flex-start;
            gap: 3rem;
            margin-top: 2rem;
            padding: 0;
            max-width: 100%;
            overflow: hidden;
            margin-bottom: 2rem;
            margin-left: 2rem;
        }

        .small-image-box {
            width: 180px;
            height: 180px;
            overflow: hidden;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .small-image-box:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .small-image-box img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all 0.3s ease;
        }

        .small-image-box:hover img {
            transform: scale(1.1);
        }

        .affiliate-form {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .affiliate-form .form-group {
            width: 100%;
        }

        .affiliate-form input {
            width: 100%;
            padding: 0.8rem;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .affiliate-form input:focus {
            border-color: var(--primary-pink);
            outline: none;
        }

        .affiliate-form input::placeholder {
            color: #888;
        }

        .affiliate-form button {
            margin-top: 1rem;
        }

        .steps {
            padding: 0;
        }

        .steps-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .steps-container h2 {
            font-size: 2rem;
            color: var(--primary-blue);
            margin-bottom: 50px;
            text-align: center;
        }

        .steps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .step-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            max-width: 350px;
            display: flex;
            flex-direction: column;
            border: 2px solid var(--primary-blue);
        }

        .step-card img {
            width: 150px;
            height: auto;
            display: block;
            margin: 20px auto;
        }

        .step-content {
            padding: 20px;
            text-align: justify;
        }

        .step-card:hover {
            box-shadow: 0 100px 500px rgba(0, 81, 255, 0.15);
        }

        .step-header {
            margin-bottom: 15px;
        }

        .step-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-pink);
            display: flex;
            align-items: center;
        }

        .step-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0.5rem 0;
            text-align: left;
        }

        .benefits {
            padding: 0;
        }

        .benefits-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .benefits-container h2 {
            font-size: 2rem;
            color: var(--primary-blue);
            margin-bottom: 50px;
            text-align: center;
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .benefit-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            max-width: 350px;
            display: flex;
            flex-direction: column;
            border: 2px solid var(--primary-blue);
        }

        .benefit-icon img {
            width: 150px;
            height: auto;
            display: block;
            margin: 20px auto;
        }

        .benefit-card p {
            padding: 20px;
            text-align: center;
            color: var(--text-dark);
        }

        .benefit-card:hover {
            box-shadow: 0 100px 500px rgba(0, 81, 255, 0.15);
        }

        .benefit-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-pink);
            margin-top: 1rem;
            text-align: center;
        }

        .cta-text {
            font-size: 1.5rem;
            margin-top: 2rem;
            font-weight: 600;
            color: var(--primary-blue);
            text-align: left;
            line-height: 1.4;
            margin-bottom: 2rem;
        }

        .start-note {
            font-size: 0.9rem;
            color: #666;
            font-style: italic;
        }

        .flow {
            padding: 2rem 0;
            background-color: var(--primary-pink);
            border-radius: 15px;
        }

        .flow-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .flow-container h2 {
            font-size: 2rem;
            color: var(--text-light);
            text-align: center;
        }

        .circular-flow-animation {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 4rem;
            margin: 3rem 0 0 0;
            padding: 1rem;
            position: relative;
        }

        .image-container {
            width: 280px;
            height: 280px;
            background: #fff;
            border-radius: 50%;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .flow-item span {
            font-size: 1.8rem;
            font-weight: 700;
            margin-top: 1.5rem;
            color: var(--text-light);
        }

        .flow-item:not(:last-child)::after {
            content: '→';
            position: absolute;
            right: -3rem;
            top: 50%;
            transform: translateY(-50%);
            font-size: 4rem;
            color: var(--yellow);
            animation: arrowPulse 1.5s infinite;
        }

        @keyframes arrowPulse {
            0% {
                transform: translateY(-50%) translateX(0);
                opacity: 1;
            }
            50% {
                transform: translateY(-50%) translateX(10px);
                opacity: 0.7;
            }
            100% {
                transform: translateY(-50%) translateX(0);
                opacity: 1;
            }
        }

        .image-container:hover {
            transform: scale(1.05);
        }

        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .flow-item {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        /* Consistent form styling */
        #affiliateRegisterModal .modal-content,
        #affiliateLoginModal .modal-content {
            max-width: 450px;
            padding: 2rem;
            width: 90%;
        }

        #affiliateRegisterModal .form-group,
        #affiliateLoginModal .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        #affiliateRegisterModal label,
        #affiliateLoginModal label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-dark);
            text-align: left;
        }

        #affiliateRegisterModal input,
        #affiliateLoginModal input {
            width: 100%;
            padding: 0.8rem 1rem;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        #affiliateRegisterModal input:focus,
        #affiliateLoginModal input:focus {
            border-color: var(--primary-pink);
            outline: none;
        }

        #affiliateRegisterModal .password-toggle,
        #affiliateLoginModal .password-toggle {
            position: absolute;
            right: 15px;
            top: 70%;
            transform: translateY(-50%);
            cursor: pointer;
            color: var(--text-gray);
            text-align: right;
        }

        #affiliateRegisterModal .btn-primary,
        #affiliateLoginModal .btn-primary {
            width: 100%;
            padding: 0.8rem;
            font-size: 1rem;
            margin-top: 1rem;
        }

        #affiliateRegisterModal .login-link,
        #affiliateLoginModal .signup-link {
            text-align: center;
            margin-top: 1.5rem;
            font-size: 0.9rem;
            color: var(--text-gray);
        }

        #affiliateRegisterModal .login-link a,
        #affiliateLoginModal .signup-link a {
            color: var(--primary-blue);
            text-decoration: none;
            font-weight: 500;
        }

        #affiliateRegisterModal .login-link a:hover,
        #affiliateLoginModal .signup-link a:hover {
            text-decoration: underline;
        }

        #affiliateErrorMessage,
        #affiliateLoginErrorMessage {
            color: red;
            margin-bottom: 1rem;
            padding: 0.5rem;
            background-color: rgba(255, 0, 0, 0.1);
            border-radius: 5px;
            display: none;
        }

        .required {
            color: red;
            margin-left: 2px;
        }

        /* Main Content Styles */
        .main-content {
            max-width: 1600px;
            margin: 0 auto;
            padding: 1rem;
        }

        /* Success modal styling */
.success-icon {
    font-size: 64px;
    color: #28a745;
    margin-bottom: 30px;
    animation: scaleIn 0.5s ease-in-out;
}

@keyframes scaleIn {
    0% { transform: scale(0); }
    60% { transform: scale(1.2); }
    100% { transform: scale(1); }
}
        /* TV */
        @media screen and (min-width: 1921px) and (max-width: 3840px) {
            .section1 .image {
                display: none;
            }
        }

        @media screen and (max-width: 1500px) {
            .navbar {
                position: relative;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.5rem;
                background: white;
                z-index: 1003;
            }
            .logo img {
                width: 4rem;
                height: 4rem;
            }
        }

        @media screen and (max-width: 1300px) {
            .search-container,
            .search-type-select,
            .search-bar {
                display: none;
            }
        }

        /* Tablets & Small Laptops */
        @media screen and (max-width: 1200px) {
            .container {
                width: 100%;
                max-width: 100%;
                margin: 0;
                padding: 0;
                overflow-x: hidden;
            }
            .navbar {
                position: relative;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.5rem;
                background: white;
                z-index: 1003;
            }
            .logo img {
                width: 4rem;
                height: 4rem;
            }
            .search-container,
            .search-type-select,
            .search-bar {
                display: none;
            }
            .hero-container {
        max-width: 95%;
        flex-direction: column;
    }

    .hero-content {
        flex: 1;
        margin-left: 1rem;
        padding-right: 1rem;
    }

    .hero-title {
        margin-top: 2rem;
        font-size: 2.5rem;
    }

    .flow-container h2 {
        font-size: 2rem;
    }
        }

        @media screen and (max-width: 1024px) {
            .hamburger {
                display: block;
            }
            .nav-links {
                display: none;
            }
            .logo h1 {
                display: none;
            }
            .logo img {
                width: 4rem;
                height: 4rem;
            }
        }

        /* Mobile Devices */
        @media screen and (max-width: 768px) {
            .intro-section, .founder-section {
                padding: 4rem 4%;
            }
            .intro-title, .founder-title {
                font-size: 2.2rem;
            }
            .intro-text, .founder-text {
                font-size: 1.1rem;
            }
            footer {
                padding: 2rem 1rem;
            }
            .footer-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
                margin-bottom: 1.5rem;
            }
            .footer-column {
                margin-bottom: 1rem;
                border-bottom: 2px solid rgba(255, 255, 255, 0.2);
                padding-bottom: 1rem;
            }
            .footer-column h3 {
                font-size: 1.5rem;
                margin-bottom: 0.8rem;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-weight: 600;
                gap: 0.5rem;
            }
            .footer-column h3::after {
                content: '▼';
                font-size: 1rem;
                transition: transform 0.3s ease;
                margin-left: auto;
            }
            .footer-column h3.active::after {
                transform: rotate(180deg);
            }
            .footer-column .footer-links {
                max-height: 0;
                overflow: hidden;
                transition: max-height 0.3s ease-out;
            }
            .footer-column .footer-links.show {
                max-height: 500px;
            }
            .footer-column a {
                font-size: 1.2rem;
                margin-bottom: 0.5rem;
                display: block;
            }
            .footer-bottom {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
                border-top: none;
                padding: 0;
            }
            .footer-bottom p {
                font-size: 1.2rem;
                margin-bottom: 0.5rem;
            }

        }

        @media screen and (max-width: 600px) {
            .sidebar {
                width: 100% !important;
                margin-bottom: 2rem;
            }
            .main-content {
                padding: 0 1rem !important;
            }
            .section-content {
                padding: 0 1rem;
            }
        }

        /* Small Mobile Devices */
        @media screen and (max-width: 480px) {
            * {
                box-sizing: border-box;
                max-width: 100%;
            }

            html, body {
                overflow-x: hidden;
                width: 100%;
                margin: 0;
                padding: 0;
            }

            /* Prevent any element from causing horizontal scroll */
            .container, .navbar, .hero, .steps, .benefits, .flow, footer {
                max-width: 100vw;
                overflow-x: hidden;
            }

            .container {
                width: 100%;
                max-width: 100%;
                margin: 0;
                padding: 0;
                overflow-x: hidden;
            }

            .navbar {
                position: relative;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.5rem 1rem;
                background: white;
                z-index: 1003;
                width: 100%;
                box-sizing: border-box;
            }

            .hamburger {
                display: block;
                flex-shrink: 0;
            }

            .nav-links {
                display: none;
            }

            .logo {
                margin-left: 0;
                flex-shrink: 0;
            }

            .logo h1 {
                display: none;
            }

            .logo img {
                width: 3rem;
                height: 3rem;
            }

            .search-container,
            .search-type-select,
            .search-bar {
                display: none;
            }

            .navbar .auth-buttons {
                display: flex;
                gap: 0.5rem;
                align-items: center;
                flex-shrink: 0;
            }

            .navbar .auth-buttons .btn {
                padding: 0.4rem 0.8rem;
                font-size: 0.8rem;
                min-width: 60px;
                height: 35px;
            }

            .hamburger.active span:nth-child(1) {
                transform: rotate(45deg) translate(5px, 5px);
            }

            .hamburger.active span:nth-child(2) {
                opacity: 0;
            }

            .hamburger.active span:nth-child(3) {
                transform: rotate(-45deg) translate(7px, -7px);
            }
            .modal-content,
            .login-modal-content,
            .forgot-password-modal,
            .security-code-modal {
                width: 95%;
                max-height: 90vh;
                overflow-y: auto;
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 10000;
                padding: 1.5rem;
                align-items: center;
                text-align: center;
            }
            .modal h2,
            .modal p,
            .modal .login-container,
            .modal .signup-link,
            .modal .login-link,
            .modal .forgot-password-link,
            .modal .checkbox-container,
            /* Hero Section Mobile Styles */
            .hero {
                padding: 0.5rem;
                margin-bottom: 2rem;
                width: 100%;
                box-sizing: border-box;
            }

            .hero-container {
                flex-direction: column;
                gap: 1.5rem;
                width: 100%;
                padding: 0;
            }

            .hero-content {
                flex: 1;
                padding: 0;
                text-align: center;
                order: 2;
            }

            .hero-image {
                flex: 1;
                order: 1;
                margin-bottom: 1rem;
            }

            .hero-image img {
                width: 100%;
                height: 250px;
                object-fit: cover;
                border-radius: 10px;
            }

            .hero-title {
                font-size: 1.5rem;
                margin-top: 1rem;
                text-align: center;
                line-height: 1.3;
            }

            .hero-content h1 {
                font-size: 1.8rem;
                text-align: center;
                line-height: 1.3;
                margin-bottom: 1rem;
            }

            .hero-highlights {
                margin: 1.5rem 0;
            }

            .hero-highlights li {
                font-size: 0.9rem;
                margin-bottom: 0.8rem;
                text-align: left;
            }

            .btn-primary {
                padding: 0.6rem 1.5rem;
                font-size: 0.9rem;
                width: auto;
                max-width: 200px;
                margin: 0 auto;
            }

            .affiliate-form {
                margin-top: 1rem;
                width: 100%;
            }

            .affiliate-form input {
                width: 100%;
                padding: 0.7rem;
                font-size: 0.9rem;
                margin-bottom: 0.8rem;
                box-sizing: border-box;
            }

            .small-images-container {
                flex-direction: column;
                align-items: center;
                gap: 1rem;
                margin: 1.5rem 0;
                margin-left: 0;
            }

            .small-image-box {
                width: 150px;
                height: 150px;
            }

            /* Steps Section Mobile Styles */
            .steps {
                padding: 2rem 1rem;
                width: 100%;
                box-sizing: border-box;
            }

            .steps-container {
                width: 100%;
                max-width: 100%;
            }

            .steps-container h2 {
                font-size: 1.5rem;
                margin-bottom: 2rem;
                text-align: center;
            }

            .steps-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
                width: 100%;
            }

            .step-card {
                max-width: 100%;
                width: 100%;
                margin: 0 auto;
                box-sizing: border-box;
            }

            .step-card img {
                width: 120px;
                height: auto;
                margin: 15px auto;
            }

            .step-content {
                padding: 15px;
                text-align: center;
            }

            .step-title {
                font-size: 1.1rem;
                text-align: center;
            }

            .step-number {
                font-size: 1.3rem;
                justify-content: center;
            }

            /* Benefits Section Mobile Styles */
            .benefits {
                padding: 2rem 1rem;
                width: 100%;
                box-sizing: border-box;
            }

            .benefits-container {
                width: 100%;
                max-width: 100%;
                padding: 20px;
            }

            .benefits-container h2 {
                font-size: 1.5rem;
                margin-bottom: 2rem;
                text-align: center;
            }

            .benefits-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
                width: 100%;
            }

            .benefit-card {
                max-width: 100%;
                width: 100%;
                margin: 0 auto;
                box-sizing: border-box;
            }

            .benefit-icon img {
                width: 120px;
                height: auto;
                margin: 15px auto;
            }

            .benefit-title {
                font-size: 1.3rem;
                text-align: center;
            }

            .benefit-card p {
                padding: 15px;
                font-size: 0.9rem;
                text-align: center;
            }

            /* Flow Section Mobile Styles */
            .flow {
                padding: 2rem 1rem;
                width: 100%;
                box-sizing: border-box;
            }

            .flow-container {
                width: 100%;
                max-width: 100%;
            }

            .flow-container h2 {
                font-size: 1.5rem;
                text-align: center;
                margin-bottom: 2rem;
            }

            .circular-flow-animation {
                flex-direction: column;
                gap: 2rem;
                margin: 2rem 0;
                padding: 1rem;
            }

            .flow-item:not(:last-child)::after {
                content: '↓';
                right: 50%;
                bottom: -2rem;
                top: auto;
                transform: translateX(50%);
                font-size: 3rem;
            }

            .image-container {
                width: 200px;
                height: 200px;
            }

            .flow-item span {
                font-size: 1.2rem;
                text-align: center;
            }

            .cta-text {
                font-size: 1.3rem;
                text-align: center;
                margin: 1.5rem 0;
                padding: 0;
                line-height: 1.5;
                max-width: 100%;
                word-wrap: break-word;
            }

            .start-note {
                font-size: 0.8rem;
                text-align: center;
                padding: 0 1rem;
            }

            /* Footer Mobile Styles */
            footer {
                padding: 2rem 1rem;
                width: 100%;
                box-sizing: border-box;
            }

            .footer-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
                margin-bottom: 1.5rem;
            }

            .footer-column {
                margin-bottom: 1rem;
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                padding-bottom: 1rem;
            }

            .footer-column h3 {
                font-size: 1.1rem;
                margin-bottom: 0.8rem;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-weight: 600;
            }

            .footer-column h3::after {
                content: '▼';
                font-size: 0.8rem;
                transition: transform 0.3s ease;
            }

            .footer-column h3.active::after {
                transform: rotate(180deg);
            }

            .footer-column .footer-links {
                max-height: 0;
                overflow: hidden;
                transition: max-height 0.3s ease-out;
            }

            .footer-column .footer-links.show {
                max-height: 500px;
            }

            .footer-column a {
                font-size: 0.9rem;
                margin-bottom: 0.5rem;
                display: block;
            }

            .footer-bottom {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
                border-top: none;
                padding: 0;
            }

            .footer-bottom p {
                font-size: 0.9rem;
                margin-bottom: 0.5rem;
            }

            .social-icons {
                display: flex;
                justify-content: center;
                margin-top: 0.5rem;
            }
            .footer-bottom .social-icons img {
                width: 1.5rem;
                height: 1.5rem;
                background: white;
                border-radius: 50%;
                padding: 0.25rem;
            }
            .social-icons a {
                margin-right: 0.2rem;
            }

            /* Modal Mobile Styles */
            .modal-content,
            .login-modal-content,
            .forgot-password-modal,
            .security-code-modal {
                width: 90%;
                max-width: 350px;
                max-height: 85vh;
                overflow-y: auto;
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 10000;
                padding: 1.2rem;
                box-sizing: border-box;
            }

            .modal h2 {
                font-size: 1.3rem;
                text-align: center;
                margin-bottom: 1rem;
            }

            .modal p,
            .modal .login-container,
            .modal .signup-link,
            .modal .login-link,
            .modal .forgot-password-link,
            .modal .checkbox-container,
            .modal .or-separator {
                text-align: center;
            }

            .form-group {
                width: 100%;
                max-width: 100%;
                text-align: left;
                margin-bottom: 1rem;
            }

            .form-group input {
                width: 100%;
                padding: 0.7rem 1rem 0.7rem 2.2rem;
                font-size: 0.9rem;
                box-sizing: border-box;
            }

            .role-selection {
                width: 100%;
                text-align: center;
            }

            .role-option {
                text-align: center;
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 1rem;
                margin: 0.8rem 0;
                font-size: 0.9rem;
            }

            .social-login-btn {
                width: 100%;
                justify-content: center;
                margin: 0.8rem auto;
                font-size: 0.9rem;
            }

            .modal-buttons {
                width: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-top: 1rem;
            }

            .modal-buttons .btn {
                min-width: 120px;
                padding: 0.7rem 1rem;
                margin: 0 auto;
                font-size: 0.9rem;
            }

            .close {
                right: 0.8rem;
                top: 0.5rem;
                font-size: 1.5rem;
            }
        }
        /* Extra small mobile devices */
        @media screen and (max-width: 360px) {
            .navbar {
                padding: 0.3rem 0.8rem;
            }

            .navbar .auth-buttons .btn {
                padding: 0.3rem 0.6rem;
                font-size: 0.75rem;
                min-width: 55px;
                height: 32px;
            }

            .logo img {
                width: 2.5rem;
                height: 2.5rem;
            }

            .hero {
                padding: 0;
            }

            .hero-title, .hero-content h1 {
                font-size: 1.3rem;
                line-height: 1.2;
            }

            .hero-highlights li {
                font-size: 0.8rem;
            }

            .small-image-box {
                width: 120px;
                height: 120px;
            }

            .btn-primary {
                font-size: 0.8rem;
            }

            .steps-container h2, .benefits-container h2 {
                font-size: 1.2rem;
            }

            .step-title, .benefit-title {
                font-size: 1rem;
            }

            .image-container {
                width: 160px;
                height: 160px;
            }

            .flow-item span {
                font-size: 1rem;
            }

            .hero-content {
                padding: 0;
            }

            .affiliate-form input {
                padding: 0.6rem;
                font-size: 0.8rem;
            }

            .cta-text {
                font-size: 1.1rem;
                line-height: 1.4;
                text-align: center;
                padding: 0;
                word-wrap: break-word;
                hyphens: auto;
            }

            .start-note {
                font-size: 0.75rem;
            }

            #affiliateRegisterModal .modal-content,
            #affiliateLoginModal .modal-content,
            #affiliateSuccessModal .modal-content {
                max-width: 320px;
                padding: 1rem;
                width: 95%;
            }

            #affiliateRegisterModal .form-group,
            #affiliateLoginModal .form-group {
                margin-bottom: 0.8rem;
            }

            #affiliateRegisterModal input,
            #affiliateLoginModal input {
                padding: 0.6rem;
                font-size: 0.8rem;
            }

            #affiliateRegisterModal .btn-primary,
            #affiliateLoginModal .btn-primary {
                padding: 0.6rem;
                font-size: 0.8rem;
            }

            #affiliateRegisterModal h2,
            #affiliateLoginModal h2,
            #affiliateSuccessModal h2 {
                font-size: 1.1rem;
            }

            #affiliateRegisterModal p,
            #affiliateLoginModal p,
            #affiliateSuccessModal p {
                font-size: 0.8rem;
            }

            #affiliateRegisterModal .login-link,
            #affiliateLoginModal .signup-link {
                font-size: 0.75rem;
            }
        }
    </style>
</head>
    <!-- Modal for Login -->
    <div id="loginModal" class="modal">
        <div class="modal-content login-modal-content">
            <span class="close" onclick="closeLoginModal()">&times;</span>
            <div class="login-container">
                <h2>Login to GigGenius</h2>
                <div id="loginErrorMessage" style="color: red; margin-bottom: 1rem; display: none;"></div>
                <form id="loginForm" method="POST" action="{{ url_for('login') }}">
                    <div class="form-group">
                        <i class="bi bi-envelope"></i>
                        <input type="email" name="email" id="email" placeholder="Email" required>
                    </div>
                    <div class="form-group">
                        <i class="bi bi-lock"></i>
                        <input type="password" name="password" id="password" placeholder="Password" required>
                    </div>
                    <div class="checkbox-container">
                        <label>
                            <input type="checkbox" id="showPassword" onclick="togglePasswordVisibility()"> Show Password
                        </label>
                        <a href="javascript:void(0)" class="forgot-password-link" onclick="openForgotPasswordModal()">Forgot Password?</a>
                    </div>
                    <button type="submit" class="btn btn-primary" style="width: auto; min-width: 150px; margin: 0 auto; display: block;">LOGIN</button>
                </form>
                <div class="or-separator">or</div>
                <a href="javascript:void(0)" class="btn btn-outline social-login-btn" onclick="signInWithGoogle()">
                    <img src="{{ url_for('static', filename='img/lp2.png') }}" alt="Google">
                    <span>Continue with Google</span>
                </a>
                <a href="javascript:void(0)" class="btn btn-outline social-login-btn" onclick="signInWithLinkedIn()">
                    <img src="{{ url_for('static', filename='img/lp5.png') }}" alt="LinkedIn">
                    <span>Continue with LinkedIn</span>
                </a>
                <div class="signup-link">
                    Don't have a GigGenius account? <a href="javascript:void(0)" onclick="closeLoginModal(); openModal();">Sign Up</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Forgot Password -->
    <div id="forgotPasswordModal" class="modal">
        <div class="modal-content forgot-password-modal">
            <span class="close" onclick="closeForgotPasswordModal()">&times;</span>
            <h2>Forgot Password</h2>
            <p>Please enter your email address to reset your password.</p>
            <input type="email" id="forgotPasswordEmail" placeholder="Email" required>
            <a href="javascript:void(0)" class="btn btn-primary" onclick="submitForgotPassword()" style="width: auto;">Submit</a>
        </div>
    </div>

    <!-- Modal for Join -->
    <div id="joinModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2>Join as Genius or Client</h2>
            <div id="roleMessage" style="color: red; display: none;">Please select a role!</div>
            <div class="role-selection">
                <div class="role-option" onclick="selectOption('genius')">
                    <input type="radio" name="role" id="geniusRole">
                    <label for="geniusRole">
                        <i class="bi bi-person"></i> I'm a Genius (Freelancer)
                    </label>
                </div>
                <div class="role-option" onclick="selectOption('client')">
                    <input type="radio" name="role" id="clientRole">
                    <label for="clientRole">
                        <i class="bi bi-briefcase"></i> I'm a Client (Business Owner)
                    </label>
                </div>
            </div>
            <div class="modal-buttons">
                <a href="javascript:void(0)" class="btn btn-primary" onclick="continueToRegistration()">Continue</a>
            </div>
            <p class="login-link">
                Already have an account? <a href="javascript:void(0)" onclick="closeModal(); openLoginModal()">Log In</a>
            </p>
        </div>
    </div>

    <!-- Affiliate Registration Modal -->
    <div id="affiliateRegisterModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeAffiliateModal()">&times;</span>
            <h2>Build a profitable network that works for you.</h2>
            <p class="subtitle" style="margin-bottom: 25px;"><span style="color: var(--primary-pink);">GigGenius &nbsp;affiliate partners are making a real impact by creating pathways to better lives.</span></p>

            <form id="affiliateForm" method="POST" action="{{ url_for('affiliate_register') }}">
                <div id="affiliateErrorMessage" style="color: red; margin-bottom: 1rem; display: none;">All fields are required</div>
                <div class="form-group">
                    <label for="firstName">First name <span class="required">*</span></label>
                    <input type="text" id="firstName" name="firstName" required>
                </div>
                <div class="form-group">
                    <label for="lastName">Last name <span class="required">*</span></label>
                    <input type="text" id="lastName" name="lastName" required>
                </div>
                <div class="form-group">
                    <label for="phone">Phone <span class="required">*</span></label>
                    <input type="tel" id="phone" name="phone" required>
                </div>
                <div class="form-group">
                    <label for="email">Email <span class="required">*</span></label>
                    <input type="email" id="email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="password">Password <span class="required">*</span></label>
                    <input type="password" id="affiliatePassword" name="password" required>
                    <i id="toggleAffiliatePassword" class="password-toggle fa fa-eye-slash" onclick="togglePasswordVisibility('affiliatePassword', 'toggleAffiliatePassword')"></i>
                </div>
                <button type="submit" class="btn btn-primary" style="width: 100%;">REGISTER</button>
            </form>

            <p class="login-link">
                Already have an affiliate account? <a href="javascript:void(0)" onclick="closeAffiliateModal(); openAffiliateLoginModal()">Log In</a>
            </p>
        </div>
    </div>

    <!-- Affiliate Login Modal -->
    <div id="affiliateLoginModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeAffiliateLoginModal()">&times;</span>
            <h2>Affiliate Login</h2>
            <div id="affiliateLoginErrorMessage" style="color: red; margin-bottom: 1rem; display: none;">All fields are required</div>
            <form id="affiliateLoginForm" method="POST" action="{{ url_for('affiliate_login') }}" onsubmit="return validateAffiliateLoginForm()">
                <div class="form-group">
                    <label for="loginEmail">Email <span class="required">*</span></label>
                    <input type="email" id="loginEmail" name="email" required>
                </div>
                <div class="form-group">
                    <label for="loginPassword">Password <span class="required">*</span></label>
                    <input type="password" id="loginPassword" name="password" required>
                    <i id="toggleLoginPassword" class="password-toggle fa fa-eye-slash" onclick="togglePasswordVisibility('loginPassword', 'toggleLoginPassword')"></i>
                </div>
                <button type="submit" class="btn btn-primary" style="width: 100%;">LOGIN</button>
            </form>
            <p class="signup-link">
                Don't have an affiliate account? <a href="javascript:void(0)" onclick="closeAffiliateLoginModal(); openAffiliateModal();">Sign Up</a>
            </p>
        </div>
    </div>

    <!-- Affiliate Success Modal -->
    <div id="affiliateSuccessModal" class="modal">
        <div class="modal-content" style="max-width: 360px;">
            <span class="close" onclick="closeAffiliateSuccessModal()">&times;</span>
            <div class="text-center">
                <i class="fas fa-check-circle success-icon"></i>
                <h2>Registration Successful!</h2>
                <p>Your affiliate application is under review. We'll notify you once it's approved.</p>
            </div>
        </div>
    </div>
        <!-- Add this side navigation panel right after the opening <body> tag -->
        <div class="side-nav" id="sideNav">
            <div class="side-nav-content">
                <div class="nav-items">
                    <a href="{{ url_for('landing_page') }}" class="nav-item">Home</a>
                    <a href="{{ url_for('find_geniuses') }}" class="nav-item">Find Geniuses</a>
                    <a href="{{ url_for('find_gigs') }}" class="nav-item">Find Gigs</a>
                    <a href="{{ url_for('about_us') }}" class="nav-item">About Us</a>
                </div>
            </div>
        </div>
        <div class="side-nav-overlay" id="sideNavOverlay" onclick="toggleMenu()"></div>

        <!-- Main Content -->
        <div class="container">
            <!-- Navbar -->
            <nav class="navbar">
                <div style="display: flex; align-items: center;">
                    <button class="hamburger" onclick="toggleMenu()">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                    <a href="{{ url_for('landing_page') }}" style="text-decoration: none;">
                        <div class="logo">
                            <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                            <h1>GigGenius</h1>
                        </div>
                    </a>
                    <div class="nav-links">
                        <a href="{{ url_for('landing_page') }}">Home</a>
                        <a href="{{ url_for('find_geniuses') }}">Find Geniuses</a>
                        <a href="{{ url_for('find_gigs') }}">Find Gigs</a>
                        <a href="{{ url_for('about_us') }}">About Us</a>
                    </div>
                </div>
                <div class="navbar-right">
                    <div class="search-auth-content">
                        <div class="search-type-select">
                            <button class="search-type-button" id="searchTypeBtn">
                                <span id="selectedSearchType">All</span>
                            </button>
                            <div class="search-type-dropdown" id="searchTypeDropdown">
                                <div class="search-type-option" data-value="all">All</div>
                                <div class="search-type-option" data-value="genius">Geniuses</div>
                                <div class="search-type-option" data-value="gigs">Gigs</div>
                            </div>
                        </div>
                        <div class="search-bar">
                            <input type="text" id="searchInput" placeholder="Search...">
                            <i class="fas fa-search icon"></i>
                        </div>
                        <div class="auth-buttons">
                            <a href="javascript:void(0)" onclick="openLoginModal()" class="btn btn-outline">Log In</a>
                            <a href="javascript:void(0)" onclick="openModal()" class="btn btn-primary">Join</a>
                        </div>
                    </div>
                </div>
            </nav>

            <main class="main-content">
            <section class="hero">
                <div class="hero-container">
                    <div class="hero-image">
                        <img src="{{ url_for('static', filename='img/ap1.jpg') }}">
                    </div>
                    <div class="hero-content">
                        <h1 class="hero-title">Become Our<span style="color: var(--primary-pink);"><br>Affiliate Partner</span></h1>
                        <p class="cta-text">As a GigGenius affiliate, you can create value for your network by sharing opportunities that help people build better careers and achieve financial independence.</p>
                        <div class="hero-highlights">
                            <ul>
                                <li><i class="fas fa-check-circle"></i> Earn 0.5% commission on every freelancer payout.</li>
                                <li><i class="fas fa-check-circle"></i> Get 1% from business owner payments.</li>
                                <li><i class="fas fa-check-circle"></i> No startup costs or monthly fees.</li>
                                <li><i class="fas fa-check-circle"></i> Access to marketing materials and support.</li>
                            </ul>
                        </div>
                        <div class="register-container">
                            <a href="javascript:void(0)" class="btn btn-primary" onclick="openAffiliateModal()">Register Now</a>
                        </div>
                        <p class="start-note">Start earning in as little as 24 hours after approval!</p>

                        <div class="small-images-container">
                            <div class="small-image-box">
                                <img src="{{ url_for('static', filename='img/ap11.jpg') }}" alt="Image 1">
                            </div>
                            <div class="small-image-box">
                                <img src="{{ url_for('static', filename='img/ap12.jpg') }}" alt="Image 2">
                            </div>
                            <div class="small-image-box">
                                <img src="{{ url_for('static', filename='img/ap13.jpg') }}" alt="Image 3">
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="steps">
                <div class="steps-container">
                    <h2>It's easy to get started</h2>
                    <div class="steps-grid">
                        <div class="step-card">
                            <img src="{{ url_for('static', filename='img/ap2.png') }}" alt="Sign Up">
                            <div class="step-content">
                                <div class="step-header">
                                    <div class="step-number">1. Join</div>
                                </div>
                                <div class="step-title">Join Our Program</div>
                                <p>It's fast and easy to get started. Complete a simple application form to join our affiliate program.</p>
                            </div>
                        </div>

                        <div class="step-card">
                            <img src="{{ url_for('static', filename='img/ap3.png') }}" alt="Promote">
                            <div class="step-content">
                                <div class="step-header">
                                    <div class="step-number">2. Promote</div>
                                </div>
                                <div class="step-title">Share With Your Audience</div>
                                <p>Share GigGenius with your network using your unique affiliate links and marketing materials.</p>
                            </div>
                        </div>

                        <div class="step-card">
                            <img src="{{ url_for('static', filename='img/ap4.png') }}" alt="Earn">
                            <div class="step-content">
                                <div class="step-header">
                                    <div class="step-number">3. Earn</div>
                                </div>
                                <div class="step-title">Receive Commissions</div>
                                <p>Start earning when your referred clients fund a project. Get paid monthly for all successful referrals.</p>
                            </div>
                        </div>
                    </div>
                    <p class="cta-text" style="text-align: center;">Join thousands of successful affiliates earning passive income while helping others succeed!</p>
                </div>
            </section>

            <section class="flow">
                <div class="flow-container">
                    <h2>Become an GigGenius Affiliate</h2>
                    <div class="icon-container">
                        <div class="circular-flow-animation">
                            <div class="flow-item">
                                <div class="image-container">
                                    <img src="{{ url_for('static', filename='img/ap5.jpg') }}" alt="JOIN">
                                </div>
                                <span>JOIN</span>
                            </div>
                            <div class="flow-item">
                                <div class="image-container">
                                    <img src="{{ url_for('static', filename='img/ap6.jpg') }}" alt="SHARE">
                                </div>
                                <span>SHARE</span>
                            </div>
                            <div class="flow-item">
                                <div class="image-container">
                                    <img src="{{ url_for('static', filename='img/ap7.jpg') }}" alt="REFER">
                                </div>
                                <span>REFER</span>
                            </div>
                            <div class="flow-item">
                                <div class="image-container">
                                    <img src="{{ url_for('static', filename='img/ap8.jpg') }}" alt="EARN">
                                </div>
                                <span>EARN</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="benefits">
                <div class="benefits-container">
                    <h2><span class="highlights">GigGenius</span> Affiliate Benefits</h2>
                    <div class="benefits-grid">
                        <div class="benefit-card">
                            <div class="benefit-icon">
                                <img src="{{ url_for('static', filename='img/ap14.png') }}" alt="Payment">
                            </div>
                            <div class="benefit-title">How GigGenius Pays You</div>
                            <p>We offer a recurring commission structure. You earn 0.5% of the commission generated by a freelancer you refer, or 1% of the weekly spending by a client you refer. This commission is paid out to you on a regular basis for as long as your referral is still using our platform.</p>
                        </div>

                        <div class="benefit-card">
                            <div class="benefit-icon">
                                <img src="{{ url_for('static', filename='img/ap15.png') }}" alt="Tracking">
                            </div>
                            <div class="benefit-title">Tracking Your Referrals</div>
                            <p>After signing up, you'll have access to a detailed affiliate dashboard. This dashboard will clearly show you which clients and freelancers you've successfully referred to GigGenius.</p>
                        </div>

                        <div class="benefit-card">
                            <div class="benefit-icon">
                                <img src="{{ url_for('static', filename='img/ap16.png') }}" alt="Withdrawal">
                            </div>
                            <div class="benefit-title">Getting Your Money</div>
                            <p>You can withdraw your earnings on a set day each month. You can choose to receive your payment via Wise or PayPal.</p>
                        </div>
                    </div>
                    <p class="cta-text" style="text-align: center;">Join the world's work marketplace as an GigGenius Affiliate today!</p>
                    <div style="text-align: center; margin-top: 1rem;">
                        <button class="btn btn-primary" onclick="openAffiliateModal()">Join Now</button>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer>
            <div class="footer-grid">
                <div class="footer-column">
                    <h3>For Clients</h3>
                    <div class="footer-links">
                    <a href="{{ url_for('how_to_hire') }}">How to Hire</a>
                    <a href="{{ url_for('accounting_services') }}">Accounting Services</a>
                    <a href="{{ url_for('events') }}">Events & Webinars</a>
                    <a href="{{ url_for('ph_business_loan') }}">PH Business Loan</a>
                    </div>
                </div>
                <div class="footer-column">
                    <h3>For Geniuses</h3>
                    <div class="footer-links">
                    <a href="{{ url_for('how_it_works') }}">How It Works?</a>
                    <a href="{{ url_for('why_cant_apply') }}">About Rejected Accounts</a>
                    <a href="{{ url_for('find_mentors') }}">GigGenius University</a>
                    <a href="{{ url_for('ph_health_insurance') }}">PH Health Insurance</a>
                    <a href="{{ url_for('ph_life_insurance') }}">PH Life Insurance</a>
                    </div>
                </div>
                <div class="footer-column">
                    <h3>Resources</h3>
                    <div class="footer-links">
                    <a href="{{ url_for('help_and_support') }}">Help & Support</a>
                    <a href="{{ url_for('news_and_events') }}">News & Events</a>
                    <a href="{{ url_for('affiliate_program') }}">Affiliate Program</a>
                    </div>
                </div>
                <div class="footer-column">
                    <h3>Company</h3>
                    <div class="footer-links">
                    <a href="{{ url_for('about_us') }}">About Us</a>
                    <a href="{{ url_for('contact_us') }}">Contact Us</a>
                    <a href="{{ url_for('charity_projects') }}">Charity Projects</a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>Follow Us:
                    <span class="social-icons">
                        <a href="https://www.facebook.com/giggenius.io" class="bi bi-facebook"></a>
                        <a href="https://www.instagram.com/gig.genius.io/" class="bi bi-instagram"></a>
                        <a href="https://www.threads.com/@gig.genius.io" class="bi bi-threads"></a>
                        <a href="https://twitter.com/giggenius_io" class="bi bi-twitter-x"></a>
                        <a href="https://www.tiktok.com/@giggenius.io" class="bi bi-tiktok"></a>
                        <a href="https://www.youtube.com/@giggenius" class="bi bi-youtube"></a>
                        <a href="https://www.linkedin.com/company/gig-genius/" class="bi bi-linkedin"></a>
                    </span>
                </p>
                <p>©2025 GigGenius by<a href="https://genuinelysolutions.com/">Genuinely Business Solutions</a></p>
                <p>
                    <a href="{{ url_for('terms_of_service') }}">Terms of Service</a> |
                    <a href="{{ url_for('privacy_policy') }}">Privacy Policy</a>
                </p>
            </div>
        </footer>
    </div>

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-33SRJGWX6H"></script>
    <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-33SRJGWX6H');
    </script>

    <script>
        // Navbar
        function toggleMenu() {
            const sideNav = document.getElementById('sideNav');
            const overlay = document.getElementById('sideNavOverlay');
            const hamburger = document.querySelector('.hamburger');
            const spans = hamburger.getElementsByTagName('span');

            sideNav.classList.toggle('active');
            overlay.classList.toggle('active');

            // Animate hamburger
            if (sideNav.classList.contains('active')) {
                spans[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
                spans[1].style.opacity = '0';
                spans[2].style.transform = 'rotate(-45deg) translate(7px, -7px)';
                document.body.style.overflow = 'hidden'; // Prevent scrolling when menu is open
            } else {
                spans[0].style.transform = 'none';
                spans[1].style.opacity = '1';
                spans[2].style.transform = 'none';
                document.body.style.overflow = 'auto'; // Restore scrolling when menu is closed
            }
        }

        // Close side nav when clicking outside
        document.addEventListener('click', (e) => {
            const sideNav = document.getElementById('sideNav');
            const hamburger = document.querySelector('.hamburger');

            if (sideNav.classList.contains('active') &&
                !sideNav.contains(e.target) &&
                !hamburger.contains(e.target)) {
                toggleMenu();
            }
        });

        document.addEventListener('DOMContentLoaded', function() {
            const footerHeadings = document.querySelectorAll('.footer-column h3');

            footerHeadings.forEach(heading => {
                heading.addEventListener('click', function() {
                    // Toggle active class on heading
                    this.classList.toggle('active');

                    // Get the next sibling element (the links container)
                    const linksContainer = this.nextElementSibling;

                    // Toggle the show class
                    linksContainer.classList.toggle('show');
                });
            });
        });

        let selectedRole = null;

        // Modal
        function closeAllModals() {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                modal.style.display = 'none';
            });
            document.body.style.overflow = 'auto';
        }
        function openModal() {
            closeAllModals();
            document.getElementById('joinModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }
        function closeModal() {
            document.getElementById('joinModal').style.display = 'none';
            document.body.style.overflow = 'auto';
            document.getElementById('roleMessage').style.display = 'none';
            document.querySelectorAll('.role-option').forEach(option => {
                option.classList.remove('selected');
            });
            document.getElementById('geniusRole').checked = false;
            document.getElementById('clientRole').checked = false;
        }

        // Join Modal
        function selectOption(role) {
            document.querySelectorAll('.role-option').forEach(option => {
                option.classList.remove('selected');
            });

            event.currentTarget.classList.add('selected');
            document.getElementById('roleMessage').style.display = 'none';

            if (role === 'genius') {
                document.getElementById('geniusRole').checked = true;
                document.getElementById('clientRole').checked = false;
            } else {
                document.getElementById('clientRole').checked = true;
                document.getElementById('geniusRole').checked = false;
            }
        }

        function continueToRegistration() {
            const geniusRole = document.getElementById('geniusRole').checked;
            const clientRole = document.getElementById('clientRole').checked;
            const roleMessage = document.getElementById('roleMessage');

            if (!geniusRole && !clientRole) {
                roleMessage.style.display = 'block';
                return;
            }

            if (geniusRole) {
                window.location.href = "{{ url_for('genius_registration') }}";
            } else {
                window.location.href = "{{ url_for('client_registration') }}";
            }
        }

        // Login Modal
        function openLoginModal() {
            closeAllModals();
            document.getElementById('loginModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }
        function closeLoginModal() {
            document.getElementById('loginModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }
        function openForgotPasswordModal() {
            closeAllModals();
            document.getElementById('forgotPasswordModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }
        function closeForgotPasswordModal() {
            document.getElementById('forgotPasswordModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }
        function togglePasswordVisibility() {
            const passwordInput = document.getElementById('password');
            passwordInput.type = passwordInput.type === 'password' ? 'text' : 'password';
        }
        function submitForgotPassword() {
            const email = document.getElementById('forgotPasswordEmail').value;
            closeForgotPasswordModal();
        }

        // Security Code Modal
        window.onclick = function(event) {
            const joinModal = document.getElementById('joinModal');
            const loginModal = document.getElementById('loginModal');
            const forgotPasswordModal = document.getElementById('forgotPasswordModal');

            if (event.target === joinModal) {
                closeModal();
            }
            if (event.target === loginModal) {
                closeLoginModal();
            }
            if (event.target === forgotPasswordModal) {
                closeForgotPasswordModal();
            }
        }

        // Scroll to Section
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Search Type Dropdown
        document.addEventListener('DOMContentLoaded', function() {
        const searchTypeBtn = document.getElementById('searchTypeBtn');
        const searchTypeDropdown = document.getElementById('searchTypeDropdown');
        const selectedSearchType = document.getElementById('selectedSearchType');
        const searchInput = document.getElementById('searchInput');
        const options = document.querySelectorAll('.search-type-option');
        // Toggle dropdown
        searchTypeBtn.addEventListener('click', function() {
            searchTypeDropdown.classList.toggle('active');
        });
        // Handle option selection
        options.forEach(option => {
            option.addEventListener('click', function() {
                const value = this.dataset.value;
                selectedSearchType.textContent = this.textContent;
                searchTypeDropdown.classList.remove('active');
                // Update placeholder based on selection
                const placeholders = {
                    genius: 'Search for genius...',
                    gigs: 'Search for gigs...',
                    projects: 'Search for projects...',
                    all: 'Search...'
                };
                searchInput.placeholder = placeholders[value] || placeholders.all;
            });
        });
        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.search-type-select')) {
                searchTypeDropdown.classList.remove('active');
            }
        });
        });

        // Login Form
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            fetch('/login', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                console.log('Login response:', data);  // Debug log

                if (data.success) {
                    window.location.href = data.redirect;
                } else {
                    const errorMessage = document.getElementById('loginErrorMessage');
                    errorMessage.textContent = data.error;
                    errorMessage.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('Login error:', error);  // Debug log
                const errorMessage = document.getElementById('loginErrorMessage');
                errorMessage.textContent = 'An error occurred during login';
                errorMessage.style.display = 'block';
            });
        });

        document.addEventListener('DOMContentLoaded', function() {
            // Get all sidebar links
            const sidebarLinks = document.querySelectorAll('.sidebar-nav a');

            // Add click event listener to each link
            sidebarLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Remove active class from all links
                    sidebarLinks.forEach(l => l.classList.remove('active'));

                    // Add active class to clicked link
                    this.classList.add('active');

                    // Scroll to the target section
                    const targetId = this.getAttribute('href');
                    const targetSection = document.querySelector(targetId);
                    if (targetSection) {
                        targetSection.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            });
        });

        // Affiliate Modal Functions
        function openAffiliateModal() {
            closeAllModals();
            document.getElementById('affiliateRegisterModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        function closeAffiliateModal() {
            document.getElementById('affiliateRegisterModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function openAffiliateLoginModal() {
            closeAllModals();
            document.getElementById('affiliateLoginModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        function closeAffiliateLoginModal() {
            document.getElementById('affiliateLoginModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // Password toggle function
        function togglePasswordVisibility(inputId, toggleId) {
            const passwordInput = document.getElementById(inputId);
            const toggleIcon = document.getElementById(toggleId);

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            }
        }

        // Affiliate Success Modal Functions
        function openAffiliateSuccessModal() {
            closeAllModals();
            document.getElementById('affiliateSuccessModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        function closeAffiliateSuccessModal() {
            document.getElementById('affiliateSuccessModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // Affiliate Registration Form Submission
        document.getElementById('affiliateForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Clear previous error
            const errorElement = document.getElementById('affiliateErrorMessage');
            errorElement.style.display = 'none';

            // Get form data
            const formData = new FormData(this);

            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.disabled = true;
            // Removed: submitBtn.textContent = 'Registering...';

            fetch('/affiliate_register', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Show success modal instead of redirecting
                    openAffiliateSuccessModal();
                } else {
                    errorElement.textContent = data.message || 'Registration failed. Please try again.';
                    errorElement.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                errorElement.textContent = 'An error occurred during registration. Please try again.';
                errorElement.style.display = 'block';
            })
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.textContent = 'REGISTER';
            });
        });

        // Affiliate Login Form Submission
        document.getElementById('affiliateLoginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Clear previous error
            const errorElement = document.getElementById('affiliateLoginErrorMessage');
            errorElement.style.display = 'none';

            // Get form data
            const formData = new FormData(this);

            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.disabled = true;

            // Add loading spinner to login button
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Please wait...';

            fetch('/affiliate_login', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    window.location.href = data.redirect;
                } else {
                    errorElement.textContent = data.error || 'Login failed';
                    errorElement.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                errorElement.textContent = 'An error occurred. Please try again.';
                errorElement.style.display = 'block';
            })
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        });
    </script>
</body>
</html>
